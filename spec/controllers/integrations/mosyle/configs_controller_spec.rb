require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::Mosyle::ConfigsController, type: :controller do
  create_company_and_user

  let(:mosyle_config) { FactoryBot.create(:mosyle_config, company: company) }

  let(:valid_mosyle_data) do
    {
      username: 'mosyle_test',
      password: 'testpassword',
      access_token: 'test_token',
      import_type: 'discovered_asset',
    }
  end

  let(:invalid_mosyle_data) do
    {
      username: '',
      password: 'testpassword',
      import_type: 'discovered_asset',
    }
  end

  let(:token_detail) do
    {
      'token' => 'mock_access_token',
      'expires' => DateTime.now + 30.minutes
    }
  end

  let(:valid_resync_params) do
    {
      id: mosyle_config.id,
      integration_name: 'mosyle',
      is_resyncing: true
    }
  end

  let(:invalid_resync_params) do
    {
      id: 99999,
      integration_name: 'mosyle',
      is_resyncing: true
    }
  end

  describe '#create' do
    context 'when credentials are valid' do
      it 'authenticates and saves credentials' do
        allow_any_instance_of(Integrations::Mosyle::FetchData).to receive(:token).and_return(token_detail)
        post :create, params: { mosyle_config: valid_mosyle_data }

        mosyle_config = Integrations::Mosyle::Config.find_by(company_id: company.id)

        expect(response).to have_http_status(:ok)
        expect(mosyle_config).not_to be_nil
        expect(mosyle_config.username).to eq('mosyle_test')
      end
    end

    context 'when credentials are invalid' do
      it 'fails to create configuration and returns an error message' do
        allow_any_instance_of(Integrations::Mosyle::FetchData).to receive(:token).and_return(token_detail)
        post :create, params: { mosyle_config: invalid_mosyle_data }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to include("Username can't be blank")
      end
    end
  end

  describe 'POST #resync' do
    context 'when the configuration exists' do
      it 'enqueues the worker and returns a 200 status' do
        post :resync, params: valid_resync_params
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when the configuration does not exist' do
      it 'returns a 404 status' do
        post :resync, params: invalid_resync_params
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'DELETE #destroy' do
    context 'when the integration is deleted successfully' do
      it 'deletes the Mosyle configuration and associated discovered assets' do
        delete :destroy, params: { id: mosyle_config.id }

        expect(response.status).to eq(200)
        expect(Integrations::Mosyle::Config.exists?(mosyle_config.id)).to eq(false)
      end
    end
  end

  describe 'POST #deactivate' do
    context 'when the integration is deactivated successfully' do
      it 'deactivates the Mosyle configuration' do
        post :deactivate, params: { id: mosyle_config.id }

        expect(response.status).to eq(200)
        expect(mosyle_config.company_integration.active).to eq(false)
      end
    end
  end
end
