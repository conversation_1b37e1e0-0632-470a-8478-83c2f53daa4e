class LogCreationWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'logs'

  def perform(log_class_name, params)
    log_class = log_class_name.constantize
    args = JSON.parse(params)
    
    sanitized_args = args.deep_transform_values do |value|
      if value.is_a?(String)
        value.gsub(Regexp.new('[\x00-\x1F\x7F-\x9F]', nil, 'n'), '')
      else
        value
      end
    end
    
    if sanitized_args['file_data'].present?
      file_data = sanitized_args['file_data']
      decoded_data = Base64.decode64(file_data['base64_data'])

      temp_file = Tempfile.new(['upload', '.zip'])
      temp_file.binmode
      temp_file.write(decoded_data)
      temp_file.rewind

      uploaded_file = ActionDispatch::Http::UploadedFile.new(
        tempfile: temp_file,
        filename: file_data['original_filename'],
        type: file_data['content_type']
      )

      log_class.create!(
        attachment: uploaded_file,
        company_id: sanitized_args['company_id']
      )
      
      temp_file.close
      temp_file.unlink
    else
      log_class.create!(sanitized_args)
    end
  rescue Exception => e
    Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
  end
end
