import permissionsHelper from 'mixins/permissions_helper';
import strings from 'mixins/string';
import { mapGetters, mapMutations } from 'vuex';

export default {
  mixins: [permissionsHelper, strings],
  data() {
    return {
      errors: [],
      isLoading: false,
      hoverIntgItem: false,
      defaultTooltipMsg: 'Something went wrong please try again with valid credentials.',
      integratedIntegrations: [],
    };
  },
  created() {
    this.setCompanyChannelKey(this.$currentCompanyGuid);
  },
  computed: {
    ...mapGetters([
      'companyIntegrations',
      'companyChannelKey',
    ]),
    ...mapGetters('GlobalStore', ['syncingConnectors']),
    isPending() {
      if (this.intgDetail) {
        if (this.intgDetail.name === 'kaseya' && !this.intgDetail.kaseyaAuthenticated) {
          return false;
        }
        return this.intgDetail.syncStatus === "pending";
      }
      return false;
    },
    isSuccess() {
      return this.intgDetail && this.intgDetail.syncStatus === "successful";
    },
    isFailed() {
      return this.intgDetail && this.intgDetail.syncStatus === "failed";
    },
    isAnyChannelConnected() {
      return this.intgDetail && this.intgDetail.isConnected;
    },
    isHelpDeskIntegration() {
      return this.intgDetail && this.intgDetail.isHelpDeskIntegration;
    },
    isDeactivated() {
      return this.intgDetail && !this.intgDetail.active;
    },
    canManage() {
      return this.isWrite || this.isScoped;
    },
    errorForUser() {
      return this.intgDetail.userErrorMessage ? this.intgDetail.userErrorMessage : this.defaultTooltipMsg;
    },
    tooltipErrorMsg() {
      if (this.intgDetail && this.intgDetail.errorMessage) {
        let superAdminError = "";
        if (this.$superAdminUser) {
          const message = this.intgDetail.errorMessage;
          let filteredMessage = message.replace(/[^a-zA-Z:0-9 ]/g, "").slice(0, 150);
          filteredMessage =  message.length > 150 ? `${filteredMessage} ...` : filteredMessage;
          superAdminError =  `Details: ${filteredMessage}`;
        }
        return `<p>${this.errorForUser}</p> ${superAdminError}`;
      }
      return "";
    },
  },
  methods: {
    ...mapMutations([
      'setCompanyChannelKey',
      'setMerakiInfo',
    ]),
    ...mapMutations('GlobalStore', [
      'setSyncingConnectors', 
      'setImportedUbiquitiDiscoveredAssets', 
      'setImportedUbiquitiManagedAssets', 
      'setImportedKaseyaDiscoveredAssets', 
      'setImportedKaseyaManagedAssets',
      'setImportedMerakiDiscoveredAssets', 
      'setImportedMerakiManagedAssets',
      'setImportedSophosDiscoveredAssets',
      'setImportedSophosManagedAssets',
      'setImportedAzureAdDiscoveredAssets',
      'setImportedAzureAdManagedAssets',
      'setImportedKandjiDiscoveredAssets',
      'setImportedKandjiManagedAssets',
      'setImportedGoogleWorkspaceDiscoveredAssets',
      'setImportedGoogleWorkspaceManagedAssets',
      'setImportedMsIntuneDiscoveredAssets',
      'setImportedMsIntuneManagedAssets',
      'setImportedJamfProDiscoveredAssets',
      'setImportedJamfProManagedAssets',
      'setImportedAzureDiscoveredAssets',
      'setImportedAzureManagedAssets',
      'setImportedAwsDiscoveredAssets',
      'setImportedAwsManagedAssets',
      'setImportedGoogleDiscoveredAssets',
      'setImportedGoogleManagedAssets',
      'setImportedMosyleDiscoveredAssets',
      'setImportedMosyleManagedAssets',
    ]),

    setupPusherListeners() {
      if (this.$pusher && this.companyChannelKey) {
        for (let i = 0; i < this.pusherIntegrations.length; i+=1) {
          const channel = this.$pusher.subscribe(this.companyChannelKey);
          channel.unbind(`${this.pusherIntegrations[i]}-config`);
          channel.bind(`${this.pusherIntegrations[i]}-config`, data => {
            if (data.value) {
              this.loadingValue[`${this.pusherIntegrations[i]}`] = data.value;
              this.addSyncingConnectors(`${this.pusherIntegrations[i]}`, data.value);
              this.setSyncingConnectors(this.updateConnectorValue(`${this.pusherIntegrations[i]}`, data.value));
              this.checkIntegrationCompletion(data.value, this.pusherIntegrations[i], `${this.pusherIntegrations[i]}`, data.count);
            }
            if (this.modulePrefix === "managed_assets" && data.isCompleted) {
              this.setMerakiInfo(data);
            }
          }, this);
        }
      }
    },
    addSyncingConnectors(intgName, value) {
      if (value) {
        const con = this.syncingConnectors.some(connector => connector.shortName === intgName);
        if (con) {
          this.setSyncingConnectors(this.updateConnectorValue(intgName, value));
        } else {
          const connector = this.onboardingOptions.find((conn) => conn.searchableName === intgName);
          this.setSyncingConnectors([
            ...this.syncingConnectors,
            {
              image: connector.imgPath,
              name: connector.name,
              shortName: connector.searchableName,
              status: 'In Progress',
              loadingValue: value,
            },
          ]);
        }
      }
    },
    updateConnectorValue(intgName, value) {
      return this.syncingConnectors.map((conn) => {
        if (conn.shortName === intgName) {
          return {
            ...conn,
            loadingValue: value,
          };
        }
        return conn;
      });
    },
    updateConnectorStatus(intgName, status) {
      return this.syncingConnectors.map((conn) => {
        if (conn.shortName === intgName) {
          return {
            ...conn,
            status,
          };
        }

        return conn;
      });
    },
    unsubscribePusher() {
      if (this.$pusher) {
        this.$pusher.unsubscribe(this.companyChannelKey);
      }
    },
    checkIntegrationCompletion(value, intgName, loadingValue, count) {
      if (value === 1) {
        this.$store.dispatch('fetchCompanyIntegrations');
        this.loadingValue[loadingValue] = 0;
        switch (intgName) {
          case "kaseya":
            this.setImportedKaseyaDiscoveredAssets(count.new_kaseya_discovered_assets);
            this.setImportedKaseyaManagedAssets(count.new_kaseya_managed_assets);
            break;
          case "ubiquiti":
            this.setImportedUbiquitiDiscoveredAssets(count.new_ubiquiti_discovered_assets);
            this.setImportedUbiquitiManagedAssets(count.new_ubiquiti_managed_assets);
            break;
          case "meraki":
            this.setImportedMerakiDiscoveredAssets(count.new_meraki_discovered_assets);
            this.setImportedMerakiManagedAssets(count.new_meraki_managed_assets);
            break;
          case "sophos":
            this.setImportedSophosDiscoveredAssets(count.new_sophos_discovered_assets);
            this.setImportedSophosManagedAssets(count.new_sophos_managed_assets);
            break;
          case "azure_ad_assets":
            this.setImportedAzureAdDiscoveredAssets(count.new_azure_ad_discovered_assets);
            this.setImportedAzureAdManagedAssets(count.new_azure_ad_managed_assets);
            break;
          case "kandji":
            this.setImportedKandjiDiscoveredAssets(count.new_Kandji_discovered_assets);
            this.setImportedKandjiManagedAssets(count.new_Kandji_managed_assets);
            break;
          case "google_workspace":
            this.setImportedGoogleWorkspaceDiscoveredAssets(count.new_google_workspace_discovered_assets);
            this.setImportedGoogleWorkspaceManagedAssets(count.new_google_workspace_managed_assets);
            break;
          case "ms_intune_assets":
            this.setImportedMsIntuneDiscoveredAssets(count.new_ms_intune_discovered_assets);
            this.setImportedMsIntuneManagedAssets(count.new_ms_intune_managed_assets);
            break;
          case "jamf_pro":
            this.setImportedJamfProDiscoveredAssets(count.new_jamf_pro_discovered_assets);
            this.setImportedJamfProManagedAssets(count.new_jamf_pro_managed_assets);
            break;
          case "azure_assets":
            this.setImportedAzureDiscoveredAssets(count.new_azure_discovered_assets);
            this.setImportedAzureManagedAssets(count.new_azure_managed_assets);
            break;
          case "aws_assets":
            this.setImportedAwsDiscoveredAssets(count.new_aws_discovered_assets);
            this.setImportedAwsManagedAssets(count.new_aws_managed_assets);
            break;
          case "google_assets":
            this.setImportedGoogleDiscoveredAssets(count.new_google_discovered_assets);
            this.setImportedGoogleManagedAssets(count.new_google_managed_assets);
            break;
          case "mosyle":
            this.setImportedMosyleDiscoveredAssets(count.new_mosyle_discovered_assets);
            this.setImportedMosyleManagedAssets(count.new_mosyle_managed_assets);
            break;
          default:
            break;
        }
        if (!this.integratedIntegrations.includes(intgName)) {
          this.emitSuccess(`${this.toTitle(intgName)} integrated successfully!`);
          this.integratedIntegrations.push(intgName);
        }
        this.setSyncingConnectors(this.updateConnectorStatus(intgName, 'Connected'));
        if (this.modulePrefix === "managed_assets") {
          this.$store.dispatch('fetchDiscoveredAssetsSummary');
        }
      } else if (value === 0) {
        this.setSyncingConnectors(this.updateConnectorStatus(intgName, 'Failed'));
        this.emitError(`${this.toTitle(intgName)} failed to integrate. Please try again.`);
        this.$store.dispatch('fetchCompanyIntegrations');
      }
    },
  },
};
