<template>
  <sweet-modal
    v-if="companyModule"
    ref="modal"
    v-sweet-esc
    modal-theme="right theme-sticky-footer"
    :title="title"
    width="41%"
    blocking
    :data-tc-field-modal="clonedAttribute.label"
    @close="resetState"
  >
    <required-field-modal
      ref="requiredFieldModal"
      @update="updatePrivate"
      @reset-permissions="resetPermissions"
    />

    <template slot="default">
      <div
        v-if="clonedAttribute"
        class="row"
      >
        <div class="col-md-5 mb-3">
          Field Type
        </div>
        <div class="col-md-7 mb-3">
          <span
            v-if="disableField"
            class="disabled-span w-100 rounded"
            data-tc-edit-type
          >
            {{ clonedAttribute.fieldAttributeType }}
          </span>
          <select
            v-else
            v-model="clonedAttribute.fieldAttributeType"
            class="form-control"
            :disabled="disableField"
            data-tc-edit-type
            @click="changeWarning"
          >
            <option
              v-for="type in attributeTypes"
              :key="type.fieldType"
              :value="type.fieldType"
            >
              {{ titleize(type.fieldType.replace("_", " ")) }}
            </option>
          </select>
        </div>
        <div class="col-md-5 mb-3">
          Visible Label
        </div>
        <div class="col-md-7 mb-3">
          <input
            v-model="clonedAttribute.label"
            type="text"
            class="form-control"
            maxlength="46"
            :data-tc-edit-label-name="clonedAttribute.label"
            data-tc-edit-label
            @keydown.enter.prevent
          >
          <span
            v-if="labelAlreadyExist"
            class="form-text small text-danger"
          >
            Label name already exist.
          </span>
          <span
            v-if="checkLabelEmpty"
            class="form-text small text-danger"
            data-tc="label can't be empty"
          >
            Label can't be empty.
          </span>
        </div>
      </div>

      <div
        v-for="(field, index) in fields"
        :key="index"
        class="mb-3"
      >
        <component
          :is="`${toHyphenCase(field.type)}Input`"
          :key="`${componentKey}-${index}`"
          ref="editFields"
          :field="field"
          :options="fieldOptions"
          :field-name ="clonedAttribute.name"
          :order-position="clonedAttribute.orderPosition"
          :list-type-val="clonedAttribute.listType"
          :is-top-positioned="isTopPositioned"
          :required="required"
          :value.sync="clonedAttribute[field.name]"
          :position="clonedAttribute.fieldPosition.position"
          :disabled-position="disabledPosition"
          :cloned-attribute.sync="clonedAttribute"
          :sort-list="sortPreferenceList()"
          :data-tc-edit-field="clonedAttribute.label"
          :data-tc-require-field="clonedAttribute.label"
          :form-field-type="clonedAttribute.fieldAttributeType"
          @add-country-state-status="addListType"
          @set-position="setPosition"
          @add-option="addOption"
          @remove-option="removeOption"
          @remove-options="removeOptions"
          @set-private="setPrivate"
          @change-preference="sortPreference"
          @change-name-format="changeNameFormat"
          @change-audience="changeAudience"
          @invalid-email="invalidEmail"
          @change-order="changeOptionPosition"
          @change-is-follower="changeIsFollower"
          @set-followers-field="setFollowersField"
        />
      </div>

      <div class="mt-4">
        <h6>Live preview:</h6>
        <div class="row">
          <div
            id="live-preview"
            class="col bg-lighter p-3 mx-3"
            :data-tc-live-preview="clonedAttribute.label"
          >
            <field-builder-preview :form-field="clonedAttribute" />
          </div>
        </div>
      </div>
      <div
        v-if="!isAllRequiredFieldsFilled"
        class="errorMsg small text-danger"
        data-tc-error="required fields"
      >
        <span class="error-rounded">
          <i class="nulodgicon-alert" />
        </span>
        Please enter all required (*) fields.
      </div>
      <div
        v-if="nameChanged"
        class="errorMsg small text-danger"
      >
        <span class="error-rounded">
          <i class="nulodgicon-alert" />
        </span>
        Please ensure to replace the text replacements in the automated tasks with the updated name of the field otherwise it'll stop working properly.
      </div>
      <div
        v-if="nameTypeError"
        class="errorMsg small text-danger"
      >
        <span class="error-rounded">
          <i class="nulodgicon-alert" />
        </span>
        Having multiple fields in different forms with same names and different types is not allowed.
      </div>
    </template>
    <button
      slot="button"
      class="btn btn-link text-secondary btn-sm"
      :data-tc-cancel-edit="clonedAttribute.label"
      :data-tc-cancel-btn="clonedAttribute.name"
      @click.stop.prevent="close"
    >
      Cancel
    </button>
    <button
      slot="button"
      class="btn btn-primary ml-2 btn-sm"
      :data-tc-save-edit="clonedAttribute.label"
      :disabled="checkDiableButton"
      :data-tc-save-disable="checkDiableButton"
      @click.stop.prevent="updateFormAttribute"
    >
      Save
    </button>
    <remove-options-modal
      ref="removeOptionsModal"
      :message="usedOptionRemoveMessage"
      :updated-options="remainingOptions"
      @update="({ value }) => setParamsFieldAttribute({ value })"
    />
  </sweet-modal>
</template>

<script>
  import { SweetModal } from 'sweet-modal-vue';
  import fieldArchitecture from 'mixins/field_architecture';
  import strings from 'mixins/string';
  import _cloneDeep from 'lodash/cloneDeep';
  import _some from 'lodash/some';
  import _reject from 'lodash/reject';
  import _get from 'lodash/get';
  import _filter from "lodash/filter";
  import http from "common/http";
  import customFormHelper from "mixins/custom_form_helper";
  import _camelCase from "lodash/camelCase";
  import permissionsHelper from 'mixins/permissions_helper';
  import validation from "mixins/custom_forms/validation";
  import RequiredFieldModal from './required_field_modal.vue';
  import * as editFormFields from '../edit_form_fields/index';
  import FieldBuilderPreview from '../field_builder_preview.vue';

  const ADDITIONAL_SORT_COLUMNS = ["ticket_number", "comment_count", "created_at", "total_time_spent", "updated_at", "company_name"];

  export default {
    components: {
      SweetModal,
      RequiredFieldModal,
      FieldBuilderPreview,
      RemoveOptionsModal: () => import('./remove_options_modal.vue'),
      ...editFormFields,
    },
    mixins: [strings, fieldArchitecture, customFormHelper, permissionsHelper, validation],
    props: ['openEditAttrModal', 'currentFormAttribute', 'attributeTypes', 'form', 'allFields', 'isFollowersPresent'],
    data() {
      return {
        clonedAttribute: _cloneDeep(this.currentFormAttribute),
        fieldArchitecture: null,
        nameChanged: false,
        clonedAttributeName: null,
        companyModule: this.getCompanyModule,
        isSaving: false,
        componentKey: 0,
        userFields: ['first_name', 'last_name', 'email'],
        disableFieldsModules: ['companyUser', 'location'],
        nameError: "",
        nameTypeError: null,
        isTypeChanged: false,
        customOptions: [],
        invalidDefaultEmail: false,
        isAllRequiredFieldsFilled: true,
        remainingOptions: [],
        remainingValue: [],
        usedOptionRemoveMessage: false,
      };
    },
    computed: {
      audience() {
        return this.cloneAttribute.audience;
      },
      isTopPositioned() {
        return this.clonedAttribute.name === 'subject';
      },
      fieldOptions() {
        if (this.clonedAttribute) {
          return typeof this.clonedAttribute.options === 'string'
                 ? JSON.parse(this.clonedAttribute.options)
                 : this.clonedAttribute.options;
        }
        return [];
      },
      title() {
        return this.clonedAttribute ? `Configure "${this.clonedAttribute.label}" Field` : 'Configure Field';
      },
      required() {
        if (this.companyModule) {
          return this[this.companyModule].DISABLED_REQUIRED.includes(this.clonedAttribute.name);
        }
        return false;
      },
      fields() {
        let fields = _get(this, 'fieldArchitecture.fields');
        if (!fields) return [];
        if (this.clonedAttribute.fieldAttributeType === 'people_list' && this.disableFieldsModules.includes(this.companyModule)) {
          fields = fields.filter(field => field.label !== 'Who to include?');
        }
        if (this.disableFieldsModules.includes(this.companyModule)) {
          return this.checkFormFields(_filter(fields, (field) => field.label !== "Page Position" && field.label !== "Is this field required to close?"));
        }
        if (this.clonedAttribute.name === 'created_by' || this.clonedAttribute.name === 'assigned_to') {
          let fieldsArray = fields;
          if (fieldsArray) {
            fieldsArray = fieldsArray.filter(field => field.type !== 'followers_toggle');
          }
          return fieldsArray;
        }
        return fields;
      },
      isPeopleModule() {
        return this.companyModule === 'companyUser';
      },
      compulsoryFieldConstant() {
        if (this.isPeopleAndCompanyNameFieldForm(this.form.formName)) {
          return 'COMPULSORY_FIELDS_WITH_COMPANY';
        }
        return 'COMPULSORY_FIELDS';
      },
      disableField() {
        if (this.companyModule) {
          return this[this.companyModule][this.compulsoryFieldConstant].includes(this.clonedAttribute.name) && !this.nameChanged;
        }
        return false;
      },
      disabledPosition() {
        if (this.companyModule) {
          return this[this.companyModule].DISABLED_POSITION.includes(this.clonedAttribute.name);
        }
        return false;
      },
      labelAlreadyExist() {
        if (this.form && this.clonedAttribute) {
          let array = this.form.formFields;
          array = _reject(array, ['orderPosition', this.clonedAttribute.orderPosition]);
          array = _reject(array, ['label', 'Add a label']);
          return  _some(array, item => item.label.toLowerCase() === this.clonedAttribute.label.toLowerCase());
        }
        return false;
      },
      checkNameEmpty() {
        return !this.form || !this.clonedAttribute || this.clonedAttribute.name === "";
      },
      checkLabelEmpty() {
        return !this.form || !this.clonedAttribute || this.clonedAttribute.label === "";
      },
      hasDefault() {
        const types = ["text", "text_area", "rich_text", "number", "date", "phone", "email", "priority", "status"];
        return types.indexOf(this.clonedAttribute.fieldAttributeType) > -1;
      },
      customList() {
        return ['status', 'list', 'tag', 'people_list', 'radio_button', 'priority', 'checkbox'].includes(this.clonedAttribute.fieldAttributeType);
      },
      checkDiableButton() {
        return this.labelAlreadyExist || this.checkNameExists() || this.checkNameEmpty || this.checkLabelEmpty || this.invalidDefaultEmail;
      },
      specialTaskError() {
        return "Sorry, you cannot remove options that are assigned to tickets or automated tasks.";
      },
    },
    watch: {
      currentFormAttribute() {
        this.clonedAttribute = _cloneDeep(this.currentFormAttribute);
        if (this.clonedAttribute && typeof(this.clonedAttribute.options) === "string") {
          this.clonedAttribute.options = JSON.parse(this.clonedAttribute.options);
        }
        this.fieldArchitecture = _cloneDeep(this.getFieldArchitecture(this.clonedAttribute, this.isFollowersPresent, this.companyModule));
      },
      'clonedAttribute.fieldAttributeType': {
        handler(value, oldValue) {
          let toggleFollowersField = this.isFollowersPresent;
          if (['created_by', 'assigned_to', 'raise_this_request_on_behalf_of'].includes(this.clonedAttribute.name)) {
            toggleFollowersField = true;
          } else if (this.clonedAttribute.name === 'followers') {
            toggleFollowersField = false;
          }
          this.fieldArchitecture = _cloneDeep(this.getFieldArchitecture(this.clonedAttribute, toggleFollowersField));
          if (value !== this.currentFormAttribute.fieldAttributeType) {
            this.isTypeChanged = true;
          }
          if (this.clonedAttribute.fieldAttributeType === "people_list" && !this.clonedAttribute.sortList) {
            this.clonedAttribute.sortList = ['Ascending', 'First Name, Last Name'];
          }
          if (value !== oldValue) {
            this.clonedAttribute.options = this.fieldArchitecture.options;
            const newFieldNumber = this.form.formFields.filter((obj) => obj.fieldAttributeType === this.clonedAttribute.fieldAttributeType).length + 1;
            this.clonedAttribute.name = `${this.clonedAttribute.fieldAttributeType} ${newFieldNumber}`;
            this.clonedAttribute.label = `${this.clonedAttribute.fieldAttributeType.replace(/_/g," ")} ${newFieldNumber}`;
          }
        },
        deep: true,
      },
      'clonedAttribute.label': {
        handler() {
          if (this.clonedAttribute && !this.clonedAttribute.id) {
            this.clonedAttributeName = this.clonedAttribute.name;
            if (this.nameAlreadyExist()) {
              this.setDefaultValues();
            }
          }
        },
        deep: true,
      },
      'clonedAttribute.name': {
        handler() {
          if (this.clonedAttribute) {
            this.clonedAttribute.name = `${this.clonedAttribute.name.toLowerCase().replace(/ /g,"_")}`;
          }
        },
        deep: true,
      },
    },
    methods: {
      onWorkspaceChange() {
        this.companyModule = _camelCase(this.getCompanyModule);
      },
      resetState() {
        this.setDefaultValues();
        this.clonedAttribute = this.currentFormAttribute;
        this.clonedAttributeName = null;
        this.close();
      },
      checkNameExists() {
        this.nameTypeError = null;
        if (this.form && this.clonedAttribute) {
          if (this.clonedAttributeName == null) {
            this.clonedAttributeName = this.clonedAttribute.name;
          }
          let array = this.form.formFields;
          this.nameChanged = this.clonedAttributeName !== this.clonedAttribute.name;
          array = _reject(array, ['orderPosition', this.clonedAttribute.orderPosition]);
          let namePresent = _some(array, item => item.name?.toLowerCase() === this.clonedAttribute.name.toLowerCase());
          if (namePresent) {
            this.nameError = "Name already exists.";
            return true;
          } else if (this.companyModule === 'helpdesk') {
            array = _reject(this.allFields, ['orderPosition', this.clonedAttribute.orderPosition]);
            namePresent = _some(array, item => item.name.toLowerCase() === this.clonedAttribute.name.toLowerCase() && item.fieldAttributeType !== this.clonedAttribute.fieldAttributeType);
            if (namePresent) {
              this.nameTypeError = true;
              return true;
            }
            if (ADDITIONAL_SORT_COLUMNS.includes(this.clonedAttribute.name)) {
              this.nameError = "This name has been reserved as a keyword. Please change the field name.";
              return true;
            }
          }
        }
        return false;
      },
      sortPreferenceList() {
        if (typeof(this.clonedAttribute.sortList) === "string") {
          this.clonedAttribute.sortList = JSON.parse(this.clonedAttribute.sortList);
        };
        return this.clonedAttribute.sortList;
      },
      close(closeModal=false) {
        this.isAllRequiredFieldsFilled = true;
        if (closeModal) {
          this.$refs.modal.close();
        }
        if (this.form.id) {
          if (!this.currentFormAttribute.id && !this.isSaving) {
            this.$emit('remove', this.currentFormAttribute);
          }
        } else if (this.clonedAttribute.isDragged && !this.isSaving) {
            this.$emit('remove', this.currentFormAttribute);
          }
      },
      open(setFields = true) {
        if (setFields && this.$refs.editFields) {
          this.$refs.editFields.forEach((editField) => {
            editField.setValueToDefault();
          });
        }
        this.clonedAttribute = _cloneDeep(this.currentFormAttribute);
        this.$refs.modal.open();
      },
      setPosition(position) {
        this.clonedAttribute.fieldPosition.position = position;
      },
      addListType(type){
        this.clonedAttribute.listType = type;
        this.clonedAttribute = _cloneDeep(this.clonedAttribute);
      },
      addOption(option) {
        if (!this.clonedAttribute.options) {
          this.clonedAttribute.options = [];
        }

        if (this.clonedAttribute.options.find(x => x === option)) {
          this.emitError(`This option already exists`);
        } else if (option == null || (typeof option === 'string' && option.trim() === '')) {
          this.emitError(`The option name can't be blank`);
        } else {
          this.customOptions.push(option);
          this.clonedAttribute.options.push(option);
          this.clonedAttribute = _cloneDeep(this.clonedAttribute);
        }
      },
      sortPreference(option) {
        this.clonedAttribute.sortList[0] = option;
        this.clonedAttribute = _cloneDeep(this.clonedAttribute);
      },
      changeNameFormat(option) {
        this.clonedAttribute.sortList[1] = option;
        this.clonedAttribute = _cloneDeep(this.clonedAttribute);
      },
      removeOption(index) {
        const idx = this.customOptions.findIndex((option) => Object.keys(option)[0] === Object.keys(this.clonedAttribute.options[index])[0]);
        if(idx > -1) {
          this.customOptions.splice(idx, 1);
        }
        const remainingValues = this.clonedAttribute.options[index];
        const removedValues = typeof(remainingValues) === 'object' ? remainingValues.name : remainingValues;
        this.clonedAttribute.options.splice(index, 1);
        this.clonedAttribute = _cloneDeep(this.clonedAttribute);
        this.remainingOptions = this.clonedAttribute.options;
        this.remainingValue.push(removedValues);
      },
      removeOptions() {
        this.clonedAttribute.options = [];
        this.clonedAttribute.listType = "";
      },
      setDefaultValues() {
        const newFieldNumber = this.form.formFields.filter((obj) => obj.fieldAttributeType === this.clonedAttribute.fieldAttributeType).length;
        if (this.checkNameExists()) {
          this.clonedAttribute.name = `${this.clonedAttribute.name}_${newFieldNumber}`;
        }
        if (this.labelAlreadyExist) {
          this.clonedAttribute.label = `${this.clonedAttribute.label} ${newFieldNumber}`;
        }
      },
      changeWarning() {
        this.emitWarning("Changing type will result in loss of data");
      },
      setParamsFieldAttribute(value) {
        const params = {
          field_values_to_update: {
            [this.clonedAttribute.id]: { removed_values: this.remainingValue, value_to_update: value.value.name },
          },
        };
        this.$emit('update-field-attributes', params);
        this.closeEditFormModal();
      },
      updateFormAttribute() {
        this.isSaving = true;
        this.clonedAttribute.isDragged = false;
        if (window.location.href.includes('related_companies')) {
          this.closeEditFormModal();
        } else {
          this.isAllRequiredFieldsFilled = this.checkValidation();
          if (!this.isAllRequiredFieldsFilled) { return; }
          if (this.customList) {
            if (this.clonedAttribute.id && this.clonedAttribute.fieldAttributeType !== 'people_list' && !this.isTypeChanged) {
              const params = { options: this.clonedAttribute.options };
              if (this.clonedAttribute.fieldAttributeType === 'status') {
                const statusOptions = this.clonedAttribute.options;
                params.options = statusOptions;
                this.clonedAttribute = { ...this.clonedAttribute, options: statusOptions };
              };
              http
                .post(`/custom_form_fields/${this.clonedAttribute.id}.json`, params)
                .then(() => {
                  this.closeEditFormModal();
                })
                .catch(err => {
                  this.clonedAttribute.options = _cloneDeep(this.currentFormAttribute.options.concat(this.customOptions));
                  this.clonedAttribute = _cloneDeep(this.clonedAttribute);
                  const errors = err.response?.data?.errors || [];
                  const hasSpecialTaskError = errors.includes(this.specialTaskError);
                   if (hasSpecialTaskError && this.companyModule === 'helpdesk') {
                    this.usedOptionRemoveMessage = true;
                    this.$refs.removeOptionsModal.open();
                    this.updateClonedAttribute();
                  } else if (errors.length) {
                    errors.forEach(e => this.emitError(e));
                  } else {
                    const associatedAutomatedTasks = this.currentFormModule() === 'tickets' ? 'or automated tasks' : '';
                    this.emitError(`Sorry, you cannot remove options that are assigned to ${this.currentFormModule()} ${associatedAutomatedTasks}`);
                  }
                });
            } else {
              this.closeEditFormModal();
            }
          } else {
            this.closeEditFormModal();
          }
        }
      },
      updateClonedAttribute() {
        this.clonedAttribute.options = this.clonedAttribute.options.filter(option =>
          this.remainingOptions.some(allowedOption => {
            const allowedName = allowedOption?.name ?? allowedOption;
            const optionName = option?.name ?? option;
            return allowedName === optionName;
          })
        );
        this.clonedAttribute = _cloneDeep(this.clonedAttribute);
      },
      checkValidation() {
        const fieldsWithOptions = ['radio_button', 'checkbox', 'list', 'status', 'tag'];
        if (fieldsWithOptions.includes(this.clonedAttribute.fieldAttributeType)) {
          return this.checkFieldOptions(this.clonedAttribute) && this.validateField(this.clonedAttribute);
        } 
        return this.validateField(this.clonedAttribute);
      },
      onToggle() {
        this.clonedAttribute.required = !this.clonedAttribute.required;
      },
      nameAlreadyExist() {
        if (this.form && this.clonedAttribute) {
          let array = this.form.formFields;
          array = _reject(array, ['orderPosition', this.clonedAttribute.orderPosition]);
          array = _reject(array, ['label', 'Add a label']);
          return  _some(array, item => item.name === this.clonedAttribute.name);
        }
        return false;
      },
      setPrivate(value) {
        if (value) {
          this.openRequiredFieldModal();
        }
      },
      updatePrivate(value) {
        this.clonedAttribute.private = !value;
        this.clonedAttribute.required = value;
        this.componentKey += 1;
      },
      resetPermissions() {
        this.$emit('reset-permissions',this.clonedAttribute);
      },
      openRequiredFieldModal() {
        this.$refs.requiredFieldModal.openFieldAlertModal();
      },
      checkFormFields(currentFormFields) {
        if (this.userFields.includes(this.clonedAttributeName)) {
          return currentFormFields.filter(field => field.name !== 'defaultValue');
        }
        return currentFormFields;
      },
      closeEditFormModal() {
        this.$emit('input', this.clonedAttribute);
        this.close(true);
        this.isSaving = false;
      },
      changeAudience(value) {
        this.clonedAttribute.audience = value.value;
        this.clonedAttribute = _cloneDeep(this.clonedAttribute);
      },
      currentFormModule() {
        if (this.isPeopleModule) {
          return 'users';
        } else if (this.companyModule === 'helpdesk') {
          return 'tickets';
        }
        return 'locations';
      },
      invalidEmail(value) {
        this.invalidDefaultEmail = value;
      },
      changeOptionPosition(options) {
        if (!this.clonedAttribute.options) {
          this.clonedAttribute.options = [];
        }
        this.clonedAttribute.options = options;
        this.clonedAttribute = _cloneDeep(this.clonedAttribute);
      },
      changeIsFollower(value) {
        this.clonedAttribute.isFollowers = value;
      },
      setFollowersField(value) {
        this.clonedAttribute.isFollowersField = value;
        if (this.clonedAttribute.isFollowersField) {
          this.clonedAttribute.name = "followers";
          this.clonedAttribute.audience = "guests";
        } else if (this.clonedAttribute.name === 'followers' && !this.clonedAttribute.isFollowersField) {
          const newFieldNumber = this.form.formFields.filter((obj) => obj.fieldAttributeType === this.clonedAttribute.fieldAttributeType).length + 1;
          this.clonedAttribute.name = `${this.clonedAttribute.fieldAttributeType} ${newFieldNumber}`;
        } 
      },
    },
  };
</script>

<style lang="scss" scoped>
  .required:after {
    content:" *";
    color: red;
  }
  .tooltip {
    &.popover {
      .popover-inner {
        background: $themed-base;
      }
    }
  }

  .errorMsg {
    padding-top: 10px;
    .error-rounded {
      display: inline-block;
      background-color: $color-error;
      border-radius: 50%;
      color: $color-error !important;
      font-size: 0.5rem;
      height: 0.8rem;
      line-height: 1rem;
      text-align: center;
      width: 0.8rem;
      .nulodgicon-alert {
        color: white;
      }
    }
  }
  .disabled-span {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    color: #6c757d;
    cursor: not-allowed;
    display: inline-block;
    padding: 8px 10px;
    user-select: none;
  }
</style>
