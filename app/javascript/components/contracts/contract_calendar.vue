<template>
  <div>
    <div class="clearfix my-5">
      <sub-menu active="calendar" />

      <div class="row justify-content-end m-0 position-relative">
        <dismissible-container
          ref="dismissibleContainer"
          :filter-contract="true"
          @close-container="toggleFilterMenu"
        >
          <contract-filters 
            @filters-updated="filtersUpdated" 
            @toggle-cost-range="toggleCostRange"
          />
          <div class="cost-range-container">
            <CostRangeFilter 
              v-if="showCostRange"
              :show-cost-range="showCostRange"
              :monthly-slider-max="monthlySliderMax"
              :total-slider-max="totalSliderMax"
              :avg-monthly-cost="avgMonthlyCost"
              :avg-total-cost="avgTotalCost"
              @apply-cost-range="applyCostRange"
            />
          </div>
        </dismissible-container>
      </div>
    </div>

    <div class="row">
      <div class="col-12 col-xxl-8">
        <calendar-view
          :available-views="views"
          :default-view="currentView"
          :contracts="contractsByMonth"
          :alerts="alertsByMonth"
          :vendors="vendorsByMonth"
          :search="contractsSearch"
          :contacts="contacts"
          :selected-contacts="contractContacts"
          :contracts-number="ContractsNumber"
          :paged-contracts="pagedContracts"
          :page="page"
          :page-count="pageCount"
          :total-count="totalCount"
          :is-loading="isPaginatedLoading"
          @select-contact="onSelectContact"
          @remove-contact="onRemoveContact"
          @update:search="onSearchUpdate"
          @view-changed="onViewChanged"
          @year-changed="onYearChanged"
          @month-changed="onMonthChanged"
          @date-selected="onDateSelected"
          @toggle-filter-menu="toggleFilterMenu"
          @reset-filters="resetFilters"
          @apply-sorting="changeSorting"
          @clear-filter="clearFilter"
          @contract-type="onContractType"
          @export="exportContractData"
          @page-changed="onPageChanged"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import { mapGetters, mapMutations } from "vuex";
  import tableLayoutStyle from 'mixins/table_layout_style';
  import permissionsHelper from 'mixins/permissions_helper';
  import sortingHelper from "mixins/sorting_helper";
  import activeSortHelper from 'mixins/active_sort_helper';
  import _orderBy from "lodash/orderBy";
  import debounce from 'lodash/debounce';
  import SubMenu from "./sub_menu.vue";
  import DismissibleContainer from "../shared/dismissible_container.vue";
  import ContractFilters from "./contract_filters.vue";
  import CalendarView from "./calender_view.vue";
  import common from '../../mixins/automated_tasks/common';
  import calendarHelper from '../../mixins/calendar_helper';
  import CostRangeFilter from './cost_range_filter.vue';

  export default {
    components: {
      SubMenu,
      DismissibleContainer,
      ContractFilters,
      CalendarView,
      CostRangeFilter,
    },
    mixins: [
      tableLayoutStyle,
      permissionsHelper,
      sortingHelper,
      activeSortHelper,
      common,
      calendarHelper,
    ],
    data() {
      return {
        contractsSearch: '',
        contacts: [],
        allContracts: [],
        contractContacts: [],
        views: ['Calendar Year', 'Upcoming 12 Months', 'Month'],
        currentView: "Calendar Year",
        currentYear: new Date().getFullYear(),
        currentMonth: new Date().getMonth(),
        selectedMonth: null,
        slot: 'list',
        pagedContracts: null,
        contractsByMonth: {},
        alertsByMonth: {},
        allContractsByMonth: {},
        selectedDate: null,
        showCostRange: false,
        monthlySliderMax: null,
        totalSliderMax: null,
        avgMonthlyCost: null,
        avgTotalCost: null,
        contractType: 'open_ended',
        page: 1,
        pageCount: 0,
        totalCount: 0,
        selectedContacts: [],
        isPaginatedLoading: false,
      };
    },
    computed: {
      ...mapGetters({
        activeCalendarFilters: "activeCalendarFilters",
        activeCalendarFiltersCount: "activeCalendarFiltersCount",
        loadingStatus: "loadingStatus",
        'contractsArr': 'contracts/contractsArr',
        'active': 'active',
        'expired': 'expired',
        'thirtyDays': 'thirtyDays',
        'sixtyDays': 'sixtyDays',
        'ninetyDays': 'ninetyDays',
        'archived': 'archived',
        'costCalculatorType': 'costCalculatorType',
        'monthlyMinCost': 'monthlyMinCost',
        'monthlyMaxCost': 'monthlyMaxCost',
        'totalMinCost': 'totalMinCost',
        'totalMaxCost': 'totalMaxCost',
        'currentStatus': 'currentStatus',
        'currentCategory': 'currentCategory',
        'currentDepartment': 'currentDepartment',
        'currentVendor': 'currentVendor',
        'currentTag': 'currentTag',
        'currentLocation': 'currentLocation',
        'prevSelectedView': 'prevSelectedView',
      }),
      vendorsByMonth() {
        return Object.fromEntries(
          Object.entries(this.contractsByMonth).map(([month, contracts]) => [
            month,
            contracts.map((contract) => contract.vendor),
          ])
        );
      },
      uniqueSelectedContractIds() {
        return [...new Set(this.contractContacts.flatMap((contact) => contact.contractIds || []))];
      },
      ContractsNumber() {
        return this.pagedContracts?.length;
      },
      alertsForMonth(month) {
        return this.alertsByMonth?.[month] || [];
      },
      isFullYearView() {
        return (
          (this.currentView === 'Calendar Year' || this.currentView === 'Upcoming 12 Months')
        );
      },
    },
    watch: {
      activeSortDirection() {
        this.applySorting();
      },
      activeSort() {
        this.applySorting();
      },
    },
    mounted() {
      this.fetchYearlyContracts();
      this.fetchContractsForMonth(1);
      this.fetchContractContacts();
      this.checkSortCookies();
      this.fetchContracts = debounce(() => {
        this.fetchYearlyContracts();
      }, 1000);
    },
    methods: {
      ...mapMutations([
        'setLoadingStatus', 
        'clearCalendarFilter',
        'setMonthlyMinCost',
        'setMonthlyMaxCost',
        'setTotalMinCost',
        'setTotalMaxCost',
      ]),

      async fetchYearlyContracts() {
        this.setLoadingStatus(true);
        try {
          const { data } = await http.get("/contracts/monthly_contracts.json", {
            params: {
              year: this.currentYear,
              contractType: this.contractType,
              currentMonth: this.currentMonth,
              search: this.contractsSearch.toLowerCase() || null,
              contacts: this.selectedContacts.length ? this.selectedContacts : null,
              view: this.currentView === 'Upcoming 12 Months' ? this.currentView : null,
              ...this.buildFilterParams(),
            },
          });
          this.originalContractsByMonth = { ...data.contracts };
          this.contractsByMonth = { ...data.contracts };
          this.alertsByMonth = { ...data.alerts };
          this.allContracts = Object.values(data.contracts).flat();
        } catch {
          this.emitError("Sorry, there was an error fetching contracts.");
        } finally {
          this.setLoadingStatus(false);
        }
      },
      async fetchContractsForMonth(page = 1) {
        this.isPaginatedLoading = true;
        try {
          const isUpcoming = this.currentView === 'Upcoming 12 Months';

          const params = {
            contractType: this.contractType,
            search: this.contractsSearch.toLowerCase() || null,
            contacts: this.selectedContacts.length ? this.selectedContacts : null,
            page,
            date: this.selectedDate && this.currentView === 'Month'
              ? `${this.selectedDate.getFullYear()}-${(this.selectedDate.getMonth() + 1).toString().padStart(2, '0')}-${this.selectedDate.getDate().toString().padStart(2, '0')}`
              : null,
            view: isUpcoming ? this.currentView : null,
            ...this.buildFilterParams(),
          };

          if ((isUpcoming || this.prevSelectedView === 'Upcoming 12 Months') && this.selectedMonth !== null && typeof this.selectedMonth === 'string') {
            const parsed = this.parseSelectedMonth(this.selectedMonth, this.currentYear);
            if (parsed) {
              params.month = parsed.month;
              params.year = parsed.year;
            }
          } else {
            params.month = this.selectedMonth != null ? this.selectedMonth + 1 : 0;
            params.year = this.currentYear;
          }

          const { data } = await http.get("/contracts/paginated_contracts.json", { params });

          this.pagedContracts = data.pagedContracts;
          this.pageCount = data.totalPages;
          this.page = data.currentPage;
          this.totalCount = data.totalCount;
        } catch {
          this.emitError("Sorry, there was an error fetching contracts");
        } finally {
          this.isPaginatedLoading = false;
        }
      },
      async fetchContractContacts() {
        try {
          const { data } = await http.get("/contracts/contract_contacts.json");
          this.contacts = data;
        } catch {
          this.emitError("Sorry, there was an error loading contract contacts.");
        }
      },
      exportContractData() {
        window.location.href = `/contracts/calendar_export?term=calendar_export&view=${this.currentView}&params=${this.tableHeading}&contract_type=${this.contractType}`;
      },
      toggleFilterMenu() {
        this.$refs.dismissibleContainer.toggleOpen();
      },
      clearFilter(filter) {
        this.clearCostFilter(filter);
        this.clearCalendarFilter(filter);
        this.filtersUpdated();
        this.resetFilters();
      },
      filtersUpdated() {
        this.applyFilters();
      },
      resetFilters() {
        Object.assign(this, {
          contractsSearch: '',
          contractContacts: [],
          contractsByMonth: { ...this.originalContractsByMonth },
        });
        this.applyFilters();
      },
      onViewChanged(newView) {
        this.currentView = newView;
        if (newView === "Calendar Year" || newView === "Upcoming 12 Months") {
          this.selectedMonth = null;
        }
        if (newView === "Month" && this.selectedMonth === null) {
          this.selectedMonth = this.currentMonth;
        }
        const thisYear = new Date().getFullYear();

        if (newView === "Upcoming 12 Months" && this.currentYear !== thisYear) {
          this.currentYear = thisYear;
        }
        this.applyFilters();
      },
      onYearChanged(newYear) {
        if (this.currentYear === newYear) return;
        this.currentYear = newYear;
        this.selectedMonth = null;
        this.fetchContracts();
        this.fetchContractsForMonth(1);
      },
      onMonthChanged(monthIndex) {
        Object.assign(this, {
          currentMonth: monthIndex,
          selectedMonth: monthIndex,
          selectedDate: null,
        });
        this.fetchContractsForMonth(1);
      },
      onDateSelected(date) {
        if (!date) {
          this.selectedDate = null;
          this.fetchContractsForMonth(1);
          return;
        }
        this.selectedDate = new Date(date);
        Object.assign(this, {
          currentMonth: this.selectedDate.getMonth(),
          currentYear: this.selectedDate.getFullYear(),
          currentView: "Month",
        });
        this.applyFilters();
      },
      applyFilters() {
        this.selectedContacts = this.contractContacts.map(contact => contact.companyUserId);
        this.fetchYearlyContracts();
        this.fetchContractsForMonth(1);
      },
      buildFilterParams() {
        return {
          category_id: this.currentCategory?.id,
          department_id: this.currentDepartment?.id,
          vendor_id: this.currentVendor?.id,
          tag: this.currentTag?.tag,
          location_id: this.currentLocation?.id,
          status: this.currentStatus?.value,
          monthly_min: this.monthlyMinCost,
          monthly_max: this.monthlyMaxCost,
          total_min: this.totalMinCost,
          total_max: this.totalMaxCost,
        };
      },
      onSelectContact(selectedContact) {
        const existingContact = this.contractContacts.find(c => c.id === selectedContact.id);
        if (existingContact) {
          existingContact.contract_ids = [...new Set([...existingContact.contract_ids, ...selectedContact.contract_ids])];
        } else {
          this.contractContacts.push({ ...selectedContact });
        }
        this.applyFilters();
      },
      onRemoveContact(removedContact) {
        this.contractContacts = this.contractContacts.filter(contact => contact.id !== removedContact.id);
        this.applyFilters();
      },
      applySorting() {
        if (!this.pagedContracts || !this.activeSort) return;
        this.pagedContracts = _orderBy(this.pagedContracts, [c => this.sortByItem(c)], [this.activeSortDirection]);
      },
      changeSorting(direction) {
        this.activeSortDirection = direction;
        this.applySorting();
      },
      toggleCostRange(showCostFilter) {
        this.showCostRange = showCostFilter !== undefined ? showCostFilter : !this.showCostRange;
        this.setSliderMax();
        this.setAverageCost();
      },
      setSliderMax() {
        if (this.allContracts?.length) {
          const monthlyCosts = this.allContracts.map(c => c.monthlyCost || 0);
          const contractValueAmounts = this.allContracts.map(c => c.contractValueAmount || 0);
          this.monthlySliderMax = Math.ceil(Math.max(...monthlyCosts));
          this.totalSliderMax = Math.ceil(Math.max(...contractValueAmounts));
        }
      },
      setAverageCost() {
        if (this.allContracts?.length) {
          const monthlyCosts = this.allContracts.map(c => c.monthlyCost || 0);
          const totalCosts = this.allContracts.map(c => c.contractValueAmount || 0);
          const avg = (arr) => arr.length ? arr.reduce((a, b) => a + b, 0) / arr.length : 0;
          this.avgMonthlyCost = Math.round(avg(monthlyCosts));
          this.avgTotalCost = Math.round(avg(totalCosts));
        }
      },
      applyCostRange() {
        this.showCostRange = false;
        this.applyFilters();
      },
      clearCostFilter(filter) {
        if (filter.filterName === "monthlyCostRange") {
          this.setMonthlyMinCost(null);
          this.setMonthlyMaxCost(null);
        } else if (filter.filterName === "totalCostRange") {
          this.setTotalMinCost(null);
          this.setTotalMaxCost(null);
        }
      },
      onContractType(contractType) {
        this.contractType = contractType;
        this.fetchYearlyContracts();
        this.fetchContractsForMonth(1);
      },
      onPageChanged(newPage) {
        this.page = newPage;
        this.fetchContractsForMonth(newPage);
      },
      onSearchUpdate(val) {
        this.contractsSearch = val;
        this.applyFilters();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .search-bar {
    width: 95%;
  }
  .contacts-bar {
    margin-left: -2rem;
    width: 60%;
  }
  .bar {
    width: 60%;
  }
  :deep(.pointer) {
    right: 2.75rem !important;
  }
  .reset-btn {
    margin-top: 0.3rem;
    color: $primary;
    font-size: 1.1rem;
    cursor: pointer;
  }
  .card-background {
    background: var(--themed-light) !important;
  }
  .export-button {
    background-color: var(--themed-box-bg) !important;
    border: 0.0625rem;
    border-radius: 0.625rem;
  }
  .list {
    background-color: var(--themed-box-bg) !important;
  }
  .card-body {
    padding-top: 0.8rem;
  }
  .number-of-contracts {
    font-size: 1.3rem;
  }
  .cost-range-container {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1050;
    width: max-content;
    background-color: white;
  }
</style>
