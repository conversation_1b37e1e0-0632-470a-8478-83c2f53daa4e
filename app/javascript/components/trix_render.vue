<template>
  <div
    :class="{
      'description-wrapper': isDescription && !isRichText,
    }"
  >
    <div v-if="viewMore && !attachmentsFiles.length">
      <div
        ref="noteBox"
        class="note-box"
        :style="{'max-height': maxHeight}"
      >
        <!-- The purpose of Iframe is to avoid overridden of app style because tickets created
             from emails have their own CSS files in comments and description.  -->
        <iframe
          :id="iFrameId"
          frameborder="0"
          :class="{ 
            'iframe': isTextOnly,
            'w-100': !isTextOnly,
          }"
          :style="{'max-height' : shouldShowHeight}"
          :srcdoc="note"
          data-tc-view="description"
          :data-tc-view-notes="note"
          @load="iframeStyles"
        />
      </div>
      <div
        id="noteFade"
        class="note-box-fade"
      />
      <a
        v-if="type == 'comment'"
        id="showBtn"
        ref="showMoreBtn"
        href="#"
        class="small py-3 cursor-pointer text-center"
        @click.stop.prevent="toggleShowMore()"
      >
        <span
          v-if="!showMoreBtn"
          class="not-as-small"
        >
          Show more
        </span>
        <span
          v-else
          class="not-as-small"
        >
          Show less
        </span>
      </a>
      <p 
        v-else
        ref="viewMoreBtn"
        class="text-center view-btn"
      >
        <a
          href="#"
          class="btn btn-text"
          @click.stop.prevent="toggleViewMore"
        >
          <div class="dot-wrapper d-inline-block text-center text-white rounded-circle">
            <i 
              class="nulodgicon-dot-3 align-top"
              :class="{ 'bg-light': isTicketEmail }"
            />
          </div>
          <span
            v-if="!viewMoreBtnExpanded"
            class="not-as-small"
          >
            View more
          </span>
          <span
            v-else
            class="not-as-small"
          >
            View less
          </span>
        </a>
      </p>
    </div>
    <!-- The purpose of Iframe is to avoid overridden of app style because tickets created
         from emails have their own CSS files in comments and description.  -->
    <div v-else>
      <!-- eslint-disable vue/no-v-html -->
      <div
        v-if="type == 'faq'"
        class="p--responsive"
        :data-tc-view-question="note"
        v-html="sanitizeHTML(note)"
      />
      <!-- eslint-enable vue/no-v-html -->
      <div v-else>
        <iframe
          :id="`note-iframe-${id}`"
          frameborder="0"
          :class="{
            'iframe': (!isDescription  || !isRichText) && !viewDescription,
            'description-iframe': isDescription || isRichText,
            'show-description': viewDescription,
          }"
          title="testPdf"
          :srcdoc="note"
          @load="iframeStyles"
        />

        <span v-if="attachmentsFiles.length">
          <div
            v-for="item in attachmentsFiles"
            :key="item.fileName"
            class="attachments"
            @click="openPreviewModal(item)"
          >
            <i class="icon genuicon-paper-clip text-muted" />
            <a href="#">{{ item.fileName }}</a>
          </div>
        </span>
      </div>
    </div>

    <Teleport to="body">
      <sweet-modal
        ref="previewModal"
        v-sweet-esc
        width="80%"
        :title="attachmentName"
      >
        <template slot="default">
          <div>
            <iframe
              width="100%"
              height="600"
              frameborder="0"
              allowfullscreen
              :src="attachmentLink"
              @load="iframeStyles"
            />
          </div>
        </template>
      </sweet-modal>
    </Teleport>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import linkIcons from 'mixins/link_icons';
import strings from 'mixins/string';
import { SweetModal } from 'sweet-modal-vue';
import permissionsHelper from 'mixins/permissions_helper';

export default {
  components: {
    SweetModal,
  },
  mixins: [linkIcons, permissionsHelper, strings],
  props: {
    value: {
      type: String,
      default: '',
    },
    action: {
      type: String,
      default: '',
    },
    id: {
      type: Number,
      default: null,
    },
    type: {
      type: String,
      default: "comment",
    },
    editable: {
      type: Boolean,
      default: false,
    },
    viewMore: {
      type: Boolean,
      default: false,
    },
    maxHeightPx: {
      type: Number,
      default: 350,
    },
    lastComment: {
      type: Boolean,
      default: false,
    },
    isEmailAttachments: {
      type: Boolean,
      default: false,
    },
    isEmailFile: {
      type: Boolean,
      default: false,
    },
    isDescription: {
      type: Boolean,
      default: false,
    },
    viewDescription: {
      type: Boolean,
      default: false,
    },
    isRichText: {
      type: Boolean,
      default: false,
    },
    isEmailCommnt: {
      type: Boolean,
      default: false,
    },
    isAttachmentComment: {
      type: Boolean,
      default: false,
    },
    isImg: {
      type: Boolean,
      default: false,
    },
    isTicketComment: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      viewMoreBtnExpanded: false,
      attachmentExists: false,
      attachmentLink: '',
      attachmentName: '',
      attachmentsFiles: [],
      pdfFileExists: false,
      pdfFileName: '',
      pdfLink: null,
      showMoreBtn: false,
    };
  },
  computed: {
    ...mapGetters(['isModernView']),
    note() {
      const html = this.value;
      let htmlObject = document.createElement('div');
      htmlObject.innerHTML = html;
      const images = htmlObject.getElementsByTagName("img");
      if (images.length > 0) {
        for (let i = 0; i < images.length; i += 1) {
          images[i].setAttribute('onclick', `window.open('${images[i].src}', '_blank');`);
        }
      }
      const links = htmlObject.getElementsByTagName("a");
      if (links.length > 0) {
        this.getIcon(links);
      }

      if (this.attachmentExists) {
        this.attachmentsFiles.forEach ( attachment => {
          const selector = htmlObject.querySelector(`[href*='${attachment.href.replace(/http[s]?:\/\//, "").replaceAll("'", "\\'")}']`);
          if (selector) selector.remove();
        });
      }
      htmlObject = htmlObject.outerHTML;
      return htmlObject;
    },
    iFrameId() {
      if (this.action) {
        return `note-iframe-${this.action}-${this.id}`;
      } 
        return `note-iframe-${this.id}`;
      
    },
    maxHeight() {
      return this.viewMoreBtnExpanded || this.showMoreBtn ? "100%" : this.maximumHeight();
    },
    isTicketEmail() {
      return this.isEmailAttachments || this.isEmailFile;
    },
    shouldShowHeight() {
      if ((this.isModernView || this.isTicketComment ) && this.type === 'comment' && !this.isAttachmentComment && !this.isEmailCommnt && !this.isImg) {
        if (this.showMoreBtn && this.$refs.showMoreBtn) {
          return '100%';
        } else if (!this.showMoreBtn && this.$refs.showMoreBtn) {
          return '6.25rem';
        }
        return `1.56rem`;
      }
      return '';
    },
    isTextOnly() {
      return !this.isModernView || this.isEmailCommnt || this.isAttachmentComment || this.isImg;
    },
  },
  watch: {
    value() {
      if (this.value) {
        this.getAttachmentLinks();
      }
    },
  },
  methods: {
    onWorkspaceChange() {
      this.getAttachmentLinks();
      if (this.$refs.viewMoreBtn) {
        this.$refs.viewMoreBtn.classList.add("hidden");
      }
      if (this.$refs.showMoreBtn) {
        this.$refs.showMoreBtn.classList.add("hidden");
      }
    },
    iframeStyles() {
      this.loadCssFile();
      if (this.type !== 'comment') {
        this.descStyle(0);
      } else {
        this.commentsStyle(0);
      }
    },
    commentsStyle(counter) {
      const iframe = document.getElementById(`note-iframe-${this.id}`);
      const {showMoreBtn} = this.$refs;
      const delay = this.isModernView ? 0 : 1000;
      if (counter < 3) {
        setTimeout(() => {
          if (iframe && 
              iframe.contentWindow && 
              iframe.contentWindow.document &&
              iframe.contentWindow.document.body && 
              showMoreBtn) {
            const height = iframe.contentWindow.document.body.scrollHeight;
            iframe.setAttribute("style", `height: ${height}px;`);
            this.appendImgStyleToIframe(iframe);
            if (height < 100) {
              showMoreBtn.classList.add("hidden");
            } else {
              showMoreBtn.classList.remove("hidden");
            }
            if (this.lastComment && counter === 0) {
              this.$emit('display-comments');
            }
            const incrementedCounter = counter + 1;
            this.commentsStyle(incrementedCounter);
          }
        }, delay);
      }
    },
    descStyle(counter) {
      let iframe;
      if (this.action) {
        iframe = document.getElementById(`note-iframe-${this.action}-${this.id}`);
      } else {
        iframe = document.getElementById(`note-iframe-${this.id}`);
      }
      if (counter < 3) {
        setTimeout(() => {
          // Ensures the iframe exists, has been properly loaded with the right markdown, and viewMoreBtn is present
          if (
            iframe &&
            iframe.contentWindow &&
            iframe.contentWindow.document &&
            iframe.contentWindow.document.body &&
            this.$refs.viewMoreBtn
          ) {
            const height = iframe.contentWindow.document.body.scrollHeight;
            iframe.setAttribute("style", `height: ${height}px;`);
            this.appendImgStyleToIframe(iframe);
            if (height < 160) {
              this.$refs.viewMoreBtn.classList.add("hidden");
              this.toggleViewMore();
            } else {
              this.$refs.viewMoreBtn.classList.remove("hidden");
            }
            if (counter === 0) {
              this.$emit('display');
            }
            const incrementedCounter = counter + 1;
            this.descStyle(incrementedCounter);
          }
        }, 600);
      }
    },
    appendImgStyleToIframe(iframe) {
      const styles = 'img { max-width:100% !important; height:auto !important;}';
      const css = document.createElement('style');
      css.type = 'text/css';
      css.appendChild(document.createTextNode(styles));
      iframe.contentDocument.head.appendChild(css);
    },

    loadCssFile() {
      const iframe = document.getElementById(`note-iframe-${this.id}`);
      const appCssLink = document.createElement("link");
      const bootstrapCustomCssLink = document.createElement("link");

      const stylesheets = document.styleSheets;
      for (let i = 0; i < stylesheets.length; i += 1) {
        if (stylesheets[i].href && stylesheets[i].href.includes("application-")) {
          appCssLink.href = stylesheets[i].href;
        } else if (stylesheets[i].href && stylesheets[i].href.includes("bootstrap-custom-")) {
          bootstrapCustomCssLink.href = stylesheets[i].href;
        }
      }

      appCssLink.rel = "stylesheet";
      bootstrapCustomCssLink.rel = "stylesheet";

      appCssLink.type = "text/css";
      bootstrapCustomCssLink.type = "text/css";

      // Keep these in this order as the main app loads them in this order
      //  which ensures various overrides we've made are also included.
      if (iframe && iframe.contentDocument.head) {
        iframe.contentDocument.head.appendChild(bootstrapCustomCssLink);
        iframe.contentDocument.head.appendChild(appCssLink);

        iframe.contentDocument.body.style.background = 'inherit';
        iframe.contentDocument.body.classList.add("trix-wrapper");

        // propogate the light/dark theme to each iframe html root
        const root = document.documentElement;
        if (root.classList.contains('dark-theme')) {
          iframe.contentWindow.document.querySelector('html').classList.remove('light-theme');
          iframe.contentWindow.document.querySelector('html').classList.add('dark-theme');
        } else {
          iframe.contentWindow.document.querySelector('html').classList.remove('dark-theme');
          iframe.contentWindow.document.querySelector('html').classList.add('light-theme');
        }
        const styles = `
          ul { list-style: inside; }
          
          .dark-theme * {
            background-color: #30435c !important;
            color: #ebeef1 !important;
          }

          .dark-theme a * {
            color: var(--themed-link) !important;
          }
        `;
        const css = document.createElement('style');
        css.type = 'text/css';
        css.appendChild(document.createTextNode(styles));
        iframe.contentDocument.head.appendChild(css);
      }
    },

    toggleViewMore() {
      this.viewMoreBtnExpanded = !this.viewMoreBtnExpanded;
      document.getElementById("noteFade").classList.toggle('note-box-fade');
    },
    openPreviewModal(item) {
      this.attachmentName = item.fileName;
      this.attachmentLink = item.href;
      this.$refs.previewModal.open();
    },
    toggleShowMore() {
      this.showMoreBtn = !this.showMoreBtn;
    },
    getAttachmentLinks() {
      this.attachmentsFiles = [];
      const html = this.value;
      const htmlObject = document.createElement('div');
      htmlObject.innerHTML = html;
      const attachments = htmlObject.getElementsByTagName('a');
      if (attachments.length > 0) {
          for (let i = 0; i < attachments.length; i += 1) {
            const allowedFiles = ['.txt', '.pdf'];
            const regex = new RegExp(`(${  allowedFiles.join('|')  }$)`);
            const validFileAttachment = regex.test(attachments[i].href.toLowerCase());
            if (validFileAttachment) {
              const {href} = attachments[i];
              let text = attachments[i].textContent;
              if (text === undefined) {
                text = attachments[i].innerText;
              }
              this.attachmentsFiles.push({ href, fileName: text });
              this.$emit('display-comments');
              this.attachmentExists = true;
            }
          }
        }
    },
    maximumHeight() {
      if (this.isTicketEmail) {
        return `${this.maxHeightPx}px`;
      }
      return this.type === 'comment' ? `6.25rem` : `${this.maxHeightPx}px`;
    },
    isEmailComment() {
      const parser = new DOMParser();
      const doc = parser.parseFromString(this.value, 'text/html');
      const elementOne = doc.getElementsByClassName('gmail_attr');
      const elementTwo = doc.getElementsByClassName('gmail_quote');
      return elementOne.length > 0 && elementTwo.length > 0;
    },
  },
};
</script>

<style scoped lang="scss">
.note-box {
  overflow:hidden;
  text-overflow: ellipsis;
}

.note-box-fade {
  content:'';
  width:100%;
  position:absolute;
  left:0;
  top:8.75rem;
}

.view-btn {
  .btn:active,
  .btn:focus,
  .btn:focus:active {
    background-image: none;
    outline: 0;
    box-shadow: none;
  }
}

.dot-wrapper {
  height: 25px;
  width: 25px;
  background-color: $blue;

  i {
    line-height: 25px;
    vertical-align: middle;
  }
}

.iframe {
  width: 100%;
  min-height: 40px;
}

.show-description {
  width: 100%;
  max-height: 30rem !important;
  min-height: 20rem !important;
}

.description-iframe {
  width: 100%;
  max-height: 30px !important;
}

.attachments {
  padding: 0.5rem 0;
}

.font-size-small {
  font-size: smaller;
  color: #313131;
}
.description-wrapper {
  width: 85% !important;
}
</style>
