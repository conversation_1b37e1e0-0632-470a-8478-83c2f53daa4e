import http from "common/http";
import _get from 'lodash/get';
import defaultStates from "./mixins/defaultStates";

export default {
  state: {
    workspaceOptions: [],
    contributorOptions: { options: {}, disableShowMore: {}, assignedToFieldOptions: [], selectedIds: [] },
    contributorOptionsWithGuests: { options: {}, disableShowMore: {} },
    errorMessage: null,
    mspCustomFormModule: '',
    customFormOptions: [],
    categories: [],
    departments: [],
    companyFilter: [],
    defaultCategories: [],
    downloadingAssetReports: [],
    isDownloadAsset: false,
    defaultDepartments: [
      { id:1, name: "Marketing"},
      { id:2, name: "Sales"},
      { id:3, name: "Engineering"},
      { id:4, name: "Managment"},
      { id:5, name: "Maintenance"},
      { id:6, name: "HR"},
      { id:7, name: "Finance"},
      { id:8, name: "Legal"},
    ],
    currentCompanyUser: null,
    mfaSettings: defaultStates.mfaSettings(),
    secureGroups: [],
    awsRegions: [],
    currentVendor: null,
    nestedModalOpened: false,
    runCaptcha: Vue.prototype.$runCaptcha,
    isRobot: Vue.prototype.$runCaptcha,
    recaptchaSiteKey: Vue.prototype.$recaptchaSiteKey, // default to true when captcha is set to run
    company: {
      name: '',
      url: '',
      subdomain: '',
    },
    domain: null,
    reroutePath: null,
    stringInterpolationKeys: [],
    syncingConnectors: [],
    importedUbiquitiDiscoveredAssets: 0,
    importedUbiquitiManagedAssets: 0,
    importedKaseyaDiscoveredAssets: 0,
    importedKaseyaManagedAssets: 0,
    importedMerakiDiscoveredAssets: 0,
    importedMerakiManagedAssets: 0,
    importedSophosDiscoveredAssets: 0,
    importedSophosManagedAssets: 0,
    importedAzureAdDiscoveredAssets: 0,
    importedAzureAdManagedAssets: 0,
    importedKandjiDiscoveredAssets: 0,
    importedKandjiManagedAssets: 0,
    importedGoogleWorkspaceDiscoveredAssets: 0,
    importedGoogleWorkspaceManagedAssets: 0,
    importedMsIntuneDiscoveredAssets: 0,
    importedMsIntuneManagedAssets: 0,
    importedJamfProDiscoveredAssets: 0,
    importedJamfProManagedAssets: 0,
    importedAzureDiscoveredAssets: 0,
    importedAzureManagedAssets: 0,
    importedAwsDiscoveredAssets: 0,
    importedAwsManagedAssets: 0,
    importedGoogleDiscoveredAssets: 0,
    importedGoogleManagedAssets: 0,
    importedMosyleDiscoveredAssets: 0,
    importedMosyleManagedAssets: 0,
    totalLocationsCount: 0,
    locations: [],
    locationLoader: false,
    userCompanies: [],
    selectedCompanies: [],
    dashboardView: localStorage.getItem('dashboardView') || 'management',
    mspData: {
      // people: [],
      // groups: [],
      categories: [],
      customForms: [],
      assetTypes: [],
      automatedTasks: [],
      responses: [],
      documents: [],
      faqs: [],
      blockedKeywords: [],
    },
    quickBuildData: {
      categories: [],
      customForms: [],
      assetTypes: [],
      automatedTasks: [],
      responses: [],
      documents: [],
      faqs: [],
      blockedKeywords: [],
    },
    loadedMspData: {
      // fetchUsers: false,
      // fetchMspGroups: false,
      fetchMspCategories: false,
      fetchMspCustomForms: false,
      fetchMspAssetTypes: false,
      fetchMspAutomatedTasks: false,
      fetchMspSnippets: false,
      fetchMspDocuments: false,
      fetchMspFaqs: false,
      fetchMspBlockedKeywords: false,
    },
    companyTemplates: [],
    loadingTemplates: true,
    isVerticalNav: false,
    selectedCardData: [],
    isMoveTicketModalOpen: false,
  },
  mutations: {
    setMoveTicketModalState: (state, value) => {
      state.isMoveTicketModalOpen = value;
    },
    setWorkspaceOptions: (state, value) => {
      state.workspaceOptions = value;
    },
    setContributorOptions(state, contributorOptions) {
      state.contributorOptions.options = {
        ...state.contributorOptions.options,
        ...contributorOptions.options,
      };

      state.contributorOptions.disableShowMore = {
        ...state.contributorOptions.disableShowMore,
        ...contributorOptions.disableShowMore,
      };
      state.contributorOptions.assignedToFieldOptions = contributorOptions.assignedToFieldOptions;
      state.contributorOptions.selectedIds = contributorOptions.selectedIds;
    },
    setContributorOptionsWithGuests(state, options) {
      state.contributorOptionsWithGuests.options = {
        ...state.contributorOptionsWithGuests.options,
        ...options.options,
      };

      state.contributorOptionsWithGuests.disableShowMore = {
        ...state.contributorOptionsWithGuests.disableShowMore,
        ...options.disableShowMore,
      };
      state.contributorOptionsWithGuests.selectedIds = options.selectedIds;
    },
    resetContributorOptionsWithGuests(state) {
      state.contributorOptionsWithGuests = { options: {}, disableShowMore: {}, selectedIds: [] };
    },
    resetContributorOptions(state) {
      state.contributorOptions = { options: {}, disableShowMore: {}, assignedToFieldOptions: [], selectedIds: [] };
    },
    setVerticalNav(state, navState) {
      state.isVerticalNav = navState;
    },
    setDashboardView(state, dashboardView) {
      state.dashboardView = dashboardView;
    },
    setSelectedCompanies(state, companies) {
      state.selectedCompanies = companies;
    },
    setUserCompanies: (state, userCompanies) => {
      state.userCompanies = userCompanies;
    },
    setErrorMessage: (state, errorMessage) => {
      (state.errorMessage = errorMessage);
    },
    setMspCustomFormModule: (state, mspCustomFormModule) => {
      (state.mspCustomFormModule = mspCustomFormModule);
    },
    setCategories: (state, categories) => {
      (state.categories = categories);
    },
    setDepartment: (state, departments) => {
      (state.departments = departments);
    },
    setCustomFormOptions: (state, options) => {
      state.customFormOptions = options;
    },
    setDefaultCategories: (state, categories) => {
      (state.defaultCategories = categories);
    },
    setDefaultDepartments: (state, departments) => {
      state.sefaultDepartments = departments;
    },
    setCurrentCompanyUser: (state, user) => {
      state.currentCompanyUser = user;
    },
    setSyncingConnectors: (state, connectors) => {
      state.syncingConnectors = connectors;
    },
    setImportedUbiquitiDiscoveredAssets: (state, count) => {
      state.importedUbiquitiDiscoveredAssets = count;
    },
    setImportedUbiquitiManagedAssets: (state, count) => {
      state.importedUbiquitiManagedAssets = count;
    },
    setImportedKaseyaDiscoveredAssets: (state, count) => {
      state.importedKaseyaDiscoveredAssets = count;
    },
    setImportedKaseyaManagedAssets: (state, count) => {
      state.importedKaseyaManagedAssets = count;
    },
    setImportedMerakiDiscoveredAssets: (state, count) => {
      state.importedMerakiDiscoveredAssets = count;
    },
    setImportedMerakiManagedAssets: (state, count) => {
      state.importedMerakiManagedAssets = count;
    },
    setImportedSophosDiscoveredAssets: (state, count) => {
      state.importedSophosDiscoveredAssets = count;
    },
    setImportedSophosManagedAssets: (state, count) => {
      state.importedSophosManagedAssets = count;
    },
    setImportedAzureAdDiscoveredAssets: (state, count) => {
      state.importedAzureAdDiscoveredAssets = count;
    },
    setImportedAzureAdManagedAssets: (state, count) => {
      state.importedAzureAdManagedAssets = count;
    },
    setImportedKandjiDiscoveredAssets: (state, count) => {
      state.importedKandjiDiscoveredAssets = count;
    },
    setImportedKandjiManagedAssets: (state, count) => {
      state.importedKandjiManagedAssets = count;
    },
    setImportedGoogleWorkspaceDiscoveredAssets: (state, count) => {
      state.importedGoogleWorkspaceDiscoveredAssets = count;
    },
    setImportedGoogleWorkspaceManagedAssets: (state, count) => {
      state.importedGoogleWorkspaceManagedAssets = count;
    },
    setImportedMsIntuneDiscoveredAssets: (state, count) => {
      state.importedMsIntuneDiscoveredAssets = count;
    },
    setImportedMsIntuneManagedAssets: (state, count) => {
      state.importedMsIntuneManagedAssets = count;
    },
    setImportedJamfProDiscoveredAssets: (state, count) => {
      state.importedJamfProDiscoveredAssets = count;
    },
    setImportedJamfProManagedAssets: (state, count) => {
      state.importedJamfProManagedAssets = count;
    },
    setImportedAzureDiscoveredAssets: (state, count) => {
      state.importedAzureDiscoveredAssets = count;
    },
    setImportedAzureManagedAssets: (state, count) => {
      state.importedAzureManagedAssets = count;
    },
    setImportedAwsDiscoveredAssets: (state, count) => {
      state.importedAwsDiscoveredAssets = count;
    },
    setImportedAwsManagedAssets: (state, count) => {
      state.importedAwsManagedAssets = count;
    },
    setImportedGoogleDiscoveredAssets: (state, count) => {
      state.importedGoogleDiscoveredAssets = count;
    },
    setImportedGoogleManagedAssets: (state, count) => {
      state.importedGoogleManagedAssets = count;
    },
    setImportedMosyleDiscoveredAssets: (state, count) => {
      state.importedMosyleDiscoveredAssets = count;
    },
    setImportedMosyleManagedAssets: (state, count) => {
      state.importedMosyleManagedAssets = count;
    },
    setLocations: (state, locations) => {
      state.locations.splice(0, state.locations.length, ...locations);
    },
    setTotalLocationsCount(state, totalLocationsCount) {
      state.totalLocationsCount = totalLocationsCount;
    },
    updateLocations(state, locations) {
      state.locations = locations;
    },
    setMfaSettings: (state, settings) => {
      state.mfaSettings = settings;
    },
    setSecureGroups: (state, secureGroups) =>{
      state.secureGroups = secureGroups;
    },
    setAwsRegions: (state, regions) => {
      (state.awsRegions = regions);
    },
    setCurrentVendor: (state, vendor) => {
      state.currentVendor = vendor;
    },
    setNestedModalOpened: (state, status) => {
      state.nestedModalOpened = status;
    },
    setRobotStatus: (state, isRobot) =>{
      state.isRobot = isRobot;
    },
    setDomain: (state, domain) => {
      state.domain = domain;
    },
    setReroutePath: (state, path) => {
      state.reroutePath = path;
    },
    setCompany: (state, company) => {
      state.company = company;
    },
    setLocationLoader: (state, locationLoader) => {
      state.locationLoader = locationLoader;
    },
    setStringInterpolationKeys: (state, keys) => {
      state.stringInterpolationKeys = keys;
    },
    setCompanyFilter: (state, companyFilter) => {
      state.companyFilter = companyFilter;
    },
    setMspData(state, mspData) {
      state.mspData[mspData.item] = mspData.data;
    },
    setQuickBuildData(state, quickBuildData) {
      state.quickBuildData[quickBuildData.item] = quickBuildData.data;
    },
    setLoadedMspData(state, loadedMspData) {
      state.loadedMspData[loadedMspData.item] = loadedMspData.flag;
    },
    setCompanyTemplates: (state, companyTemplates) => { state.companyTemplates = companyTemplates; },
    setLoadingTemplates: (state, loadingTemplates) => { state.loadingTemplates = loadingTemplates; },
    setCardData(state, payload) {
      state.selectedCardData = payload.selectedItems;
    },
    setIntUserData(state, userData) {
      state.intUserData = userData;
    },
    updateDownloadingAssetReports: (state, payload) => {
      state.downloadingAssetReports = [...payload];
    },
    setIsDownloadAsset(state, value) {
      state.isDownloadAsset = value;
    },
  },
  getters: {
    isMoveTicketModalOpen: (state) => state.isMoveTicketModalOpen,
    workspaceOptions: (state) => state.workspaceOptions,
    contributorOptions: (state) => state.contributorOptions,
    contributorOptionsWithGuests: (state) => state.contributorOptionsWithGuests,
    isVerticalNav: state => state.isVerticalNav,
    companyFilter: state => state.companyFilter,
    dashboardView: (state) => state.dashboardView,
    selectedCompanies: (state) => state.selectedCompanies,
    userCompanies: (state) => state.userCompanies,
    mspCustomFormModule: (state) => state.mspCustomFormModule,
    errorMessage: (state) => state.errorMessage,
    customFormOptions: (state) => state.customFormOptions,
    categories: (state) => state.categories,
    departments: (state) => state.departments,
    defaultCategories: (state) => state.defaultCategories,
    defaultDepartments: (state) => state.defaultDepartments,
    currentCompanyUser: (state) => state.currentCompanyUser,
    mfaSettings: (state) => state.mfaSettings,
    secureGroups: (state) => state.secureGroups,
    awsRegions: (state) => state.awsRegions,
    currentVendor: state => state.currentVendor,
    nestedModalOpened: state => state.nestedModalOpened,
    isRobot: state => state.isRobot,
    runCaptcha: state => state.runCaptcha,
    recaptchaSiteKey: state => state.recaptchaSiteKey,
    totalLocationsCount: state => state.totalLocationsCount,
    domain: state => state.domain,
    reroutePath: state => state.reroutePath,
    company: state => state.company,
    locations: state => state.locations,
    locationLoader: state => state.locationLoader,
    stringInterpolationKeys: state => state.stringInterpolationKeys,
    syncingConnectors: state => state.syncingConnectors,
    importedUbiquitiDiscoveredAssets: state => state.importedUbiquitiDiscoveredAssets,
    importedUbiquitiManagedAssets: state => state.importedUbiquitiManagedAssets,
    importedKaseyaDiscoveredAssets: state => state.importedKaseyaDiscoveredAssets,
    importedKaseyaManagedAssets: state => state.importedKaseyaManagedAssets,
    importedMerakiDiscoveredAssets: state => state.importedMerakiDiscoveredAssets,
    importedMerakiManagedAssets: state => state.importedMerakiManagedAssets,
    importedSophosDiscoveredAssets: state => state.importedSophosDiscoveredAssets,
    importedSophosManagedAssets: state => state.importedSophosManagedAssets,
    importedAzureAdDiscoveredAssets: state => state.importedAzureAdDiscoveredAssets,
    importedAzureAdManagedAssets: state => state.importedAzureAdManagedAssets,
    importedKandjiDiscoveredAssets: state => state.importedKandjiDiscoveredAssets,
    importedKandjiManagedAssets: state => state.importedKandjiManagedAssets,
    importedGoogleWorkspaceDiscoveredAssets: state => state.importedGoogleWorkspaceDiscoveredAssets,
    importedGoogleWorkspaceManagedAssets: state => state.importedGoogleWorkspaceManagedAssets,
    importedMsIntuneDiscoveredAssets: state => state.importedMsIntuneDiscoveredAssets,
    importedMsIntuneManagedAssets: state => state.importedMsIntuneManagedAssets,
    importedJamfProDiscoveredAssets: state => state.importedJamfProDiscoveredAssets,
    importedJamfProManagedAssets: state => state.importedJamfProManagedAssets,
    importedAzureDiscoveredAssets: state => state.importedAzureDiscoveredAssets,
    importedAzureManagedAssets: state => state.importedAzureManagedAssets,
    importedAwsDiscoveredAssets: state => state.importedAwsDiscoveredAssets,
    importedAwsManagedAssets: state => state.importedAwsManagedAssets,
    importedGoogleDiscoveredAssets: state => state.importedGoogleDiscoveredAssets,
    importedGoogleManagedAssets: state => state.importedGoogleManagedAssets,
    importedMosyleDiscoveredAssets: state => state.importedMosyleDiscoveredAssets,
    importedMosyleManagedAssets: state => state.importedMosyleManagedAssets,
    mspData: state => state.mspData,
    quickBuildData: state => state.quickBuildData,
    loadedMspData: state => state.loadedMspData,
    companyTemplates: state => state.companyTemplates,
    loadingTemplates: state => state.loadingTemplates,
    selectedCardData: state => state.selectedCardData,
    intUserData: state => state.intUserData,
    downloadingAssetReports: state => state.downloadingAssetReports,
    isDownloadAsset: state => state.isDownloadAsset,
  },
  actions: {
    async setupOptionsPusher({ commit }, payload) {
      const pusher = await Vue.prototype.$pusher;
      const channel = pusher.subscribe(Vue.prototype.$currentCompanyGuid);
      if (payload.type === 'contributorOptions') {
        channel.bind('contributor_options', data => {
          if (data.is_cache_reset) {
            commit('resetContributorOptions');
            commit('resetContributorOptionsWithGuests');
          }
        });
      } else if (payload.type === 'workspaceOptions') {
        channel.bind('workspace_options', data => {
          if (data.is_cache_reset) {
            commit('setWorkspaceOptions', []);
          }
        });
      }
    },
    async setupPusherListener({ dispatch, commit }) {
      const pusher = await Vue.prototype.$pusher;
      const currentCompanyGuid = Vue.prototype.$currentCompanyGuid;
      if (!pusher || !currentCompanyGuid) return;

      const channel = pusher.subscribe(currentCompanyGuid);
      channel.bind('download-asset-excel', data => {
        const { excelFile, report } = data;
        dispatch('updateReportPercentage', report);
        commit('setIsDownloadAsset', true);
        if (report.percentage === 1) {
          dispatch('downloadReport', excelFile);
          dispatch('resetExcelExportLink', Vue.prototype.$currentCompanyUserId);
        }
      }, this);
    },
    downloadReport({commit}, excelFileUrl) {
      if (!excelFileUrl) {
        commit('setErrorMessage', 'No file URL available to download.');
        return;
      }

      const link = document.createElement('a');
      link.href = excelFileUrl;
      link.download = excelFileUrl.split('/').pop();
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    updateReportPercentage({ commit, state }, data) {
      let reportIndex = state.downloadingAssetReports.findIndex((r) => r.trackId === data.trackId);

      if (reportIndex === -1) {
        reportIndex = state.downloadingAssetReports.findIndex(
          (r) => !r.trackId && r.name === data.name && r.percentage === 0
        );
      }
      if (reportIndex !== -1) {
        const reports = [...state.downloadingAssetReports];
        reports[reportIndex] = { ...data };
        commit('updateDownloadingAssetReports', reports);
      } else {
        commit('updateDownloadingAssetReports', [...state.downloadingAssetReports, { ...data }]);
      }
    },
    checkReportProgess({ dispatch, commit }, contributorId) {
      const exportData = JSON.parse(localStorage.getItem('asset_export_status'));
      if (!exportData) return;

      http
        .get('/managed_assets/get_asset_report_record', {
          params: { contributor_id: contributorId },
        })
        .then((res) => {
          const { report } = res.data;
          const { percentage, downloadLink, readyToDownload } = report;
          const shouldTrackProgress = percentage !== 1.0 && downloadLink === '' && !readyToDownload;
          const isReadyToDownload = percentage === 1.0 && downloadLink !== '' && readyToDownload;

          if (shouldTrackProgress || isReadyToDownload) {
            commit('setIsDownloadAsset', true);
            dispatch('updateReportPercentage', {
              name: report.name,
              trackId: report.trackId,
              percentage,
              timestamp: new Date().getTime(),
              readyToDownload,
              downloadLink,
            });
          }
          if (isReadyToDownload) {
            dispatch('downloadReport', downloadLink);
            dispatch('resetExcelExportLink', contributorId);
          }
        })
        .catch((error) => {
          commit('setErrorMessage', `Sorry, there was an error fetching Asset Excel Report (${error.response?.data?.message || error.message})`);
        });
    },
    resetExcelExportLink( { commit }, contributorId) {
      http
        .post('/managed_assets/reset_download_link', {
          contributor_id: contributorId,
        })
        .then(() => {
          localStorage.removeItem('asset_export_status');
        })
        .catch((error) => {
          commit('setErrorMessage', 'Failed to reset Excel File download link', error.response?.data?.message || error.message);
        });
    },
    fetchWorkspaces({ commit }) {
      http
        .get(`/workspace_options.json`)
        .then((res) => {
          commit('setWorkspaceOptions', res.data);
        })
        .catch((error) => {
          this.emitError(`Sorry, there was an error loading workspaces. (${error.response.data.message}).`);
        });
    },
    fetchUserCompanies({ commit }) {
      return http
        .get("/company_options.json")
        .then((res) => {
          commit("setUserCompanies", res.data.filter(c => c.subdomain !== 'sample'));
        })
        .catch(() => {
          this.emitError('Sorry, there was an error loading your companies.');
        });
    },
    fetchCustomFormOptions({ commit }, params) {
      return http
        .get(`/custom_form_options.json`, { params })
        .then(res => {
          commit('setCustomFormOptions', res.data);
        })
        .catch(() => {
          this.emitError('Sorry, there was an error loading forms. Please try again later.');
        });
    },
    fetchCategories({ commit }) {
      return http
        .get("/categories.json")
        .then((res) => {
          commit("setCategories", res.data);
        })
        .catch(() => {
          commit(
            "setError",
            `Sorry, there was an error gathering categories data. Please refresh the page and try again.`
          );
        });
    },
    fetchDefaultCategories({ commit }) {
      http
        .get(`/default_categories.json`)
        .then((res) => {
          commit("setDefaultCategories", res.data);
        })
        .catch(() => {
          commit("setError", `Sorry, there was an error fetching default categories. Please refresh the page and try again.`);
        });
    },
    fetchDepartments({ commit }) {
      return http
        .get('/departments.json')
        .then((res) => {
          commit("setDepartment", res.data.companyDepartments);
        })
        .catch(() => {
          commit('Sorry, there was an error fetching company departments. Please refresh the page and try again.');
        });
    },
    fetchCurrentCompanyUser({ commit }, params ) {
      http
        .get(`/current_company_user.json`, { params })
        .then((res) => {
          commit("setCurrentCompanyUser", res.data);
        })
        .catch((error) => {
          commit(
            "setErrorMessage",
            `Sorry, there was an error loading user (${error.response.data.message}).`
          );
        });
    },
    fetchMfaSettings({ commit }, companyId) {
      const url = "/mfa/settings.json";
      const params = { company_id: companyId };
      return http
        .get(url, { params })
        .then((res) => {
          if (res.data.mfaSettings) {
            commit("setMfaSettings", res.data.mfaSettings);
            commit("setSecureGroups", res.data.secureGroups);
          }
        })
        .catch(() => {
          commit("setError", `Sorry, there was an error while loading two factor authentication status.`);
        });
    },
    resetMfaSettings({ commit }) {
      commit("setMfaSettings", defaultStates.mfaSettings());
    },
    fetchAwsRegions({ commit }) {
      http
        .get("/integrations/aws_assets/regions.json")
        .then((res) => {
          commit("setAwsRegions", res.data.awsRegions);
        })
        .catch(() => {
          commit("setError", `Sorry, there was an error gathering aws regions. Please refresh the page and try again.`);
        });
    },
    fetchVendor({ commit, rootState }, id) {
      commit('setLoadingStatus', true);
      return http
        .get(`/vendors/${id}.json`)
        .then(res => {
          commit('setCurrentVendor', res.data);
          rootState.loadingStatus = false;
          rootState.loading = false;
        });
    },
    fetchStringInterpolationKeys({ commit }, type) {
      return http
        .get('/string_interpolation_keys.json', { params: { type } })
        .then((res) => {
          commit('setStringInterpolationKeys', res.data);
        });
    },
    fetchLocations({ commit }, params={}) {
      commit("setLocationLoader", true);
      http
        .get('/location_options.json', { params })
        .then((res) => {
          commit("setLocations", res.data);
          if (res.data.length > 0) {
            commit("setTotalLocationsCount", res.data[0].totalLocationsCount);
          }
        })
        .catch((error) => {
          const msg = _get(error, 'response.data.message', "");
          commit("setErrorMessage", `Sorry, there was an error loading location info. ${msg}`);
        })
        .finally(() => {
          commit("setLocationLoader", false);
        });
    },
    fetchMspAssetTypes({commit}, params={}) {
      return http
        .get(`/msp/templates/asset_types.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data.mspAssetTypes, item: "assetTypes" });
          } else {
            commit('setMspData', { data: res.data.mspAssetTypes, item: "assetTypes" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspAssetTypes" });
        });
    },
    fetchMspBlockedKeywords({commit}, params={}) {
      return http
        .get(`/msp/templates/blocked_keywords.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data.mspBlockedKeywords, item: "blockedKeywords" });
          } else {
            commit('setMspData', { data: res.data.mspBlockedKeywords, item: "blockedKeywords" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspBlockedKeywords" });
        });
    },
    fetchMspCategories({commit}, params={}) {
      return http
        .get(`/msp/templates/categories.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data.mspCategories, item: "categories" });
          } else {
            commit('setMspData', { data: res.data.mspCategories, item: "categories" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspCategories" });
        });
    },
    fetchMspDocuments({commit}, params={}) {
      return http
        .get(`/msp/templates/library_documents.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data.mspTemplatesDocuments, item: "documents" });
          } else {
            commit('setMspData', { data: res.data.mspTemplatesDocuments, item: "documents" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspDocuments" });
        });
    },
    fetchUsers({commit}) {
      return http
        .get(`/msp/templates/company_users.json`)
        .then((res) => {
          commit('setMspData', { data: res.data.companyUsers, item: "people" });
          commit('setLoadedMspData', { flag: true, item: "fetchUsers" });
        });
    },
    fetchMspSnippets({commit}, params={}) {
      return http
        .get(`/msp/templates/snippets.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data.mspSnippets, item: "responses" });
          } else {
            commit('setMspData', { data: res.data.mspSnippets, item: "responses" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspSnippets" });
        });
    },
    fetchMspCustomForms({commit}, params={}) {
      return http
        .get(`/msp/templates/custom_forms.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data.mspCustomForms, item: "customForms" });
          } else {
            commit('setMspData', { data: res.data.mspCustomForms, item: "customForms" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspCustomForms" });
        });
    },
    fetchMspFaqs({commit}, params={}) {
      return http
        .get(`/msp/templates/faqs.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data.mspTemplatesHelpdeskFaqs, item: "faqs" });
          } else {
            commit('setMspData', { data: res.data.mspTemplatesHelpdeskFaqs, item: "faqs" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspFaqs" });
        });
    },
    fetchMspGroups({commit}) {
      return http
        .get(`/msp/templates/groups.json`)
        .then((res) => {
          commit('setMspData', { data: res.data.mspGroups, item: "groups" });
          commit('setLoadedMspData', { flag: true, item: "fetchMspGroups" });
        });
    },
    fetchMspAutomatedTasks({commit}, params={}) {
      return http
        .get(`/msp/templates/automated_tasks.json`, { params })
        .then((res) => {
          if (params.excludes_ids) {
            commit('setQuickBuildData', { data: res.data, item: "automatedTasks" });
          } else {
            commit('setMspData', { data: res.data, item: "automatedTasks" });
          }
          commit('setLoadedMspData', { flag: true, item: "fetchMspAutomatedTasks" });
        });
    },
    fetchCompanyTemplates({commit}) {
      return http
        .get('/company_builds.json')
        .then((res) => {
          commit('setCompanyTemplates', res.data.companyBuilds);
          commit('setLoadingTemplates', false);
        });
    },
    async fetchAssetSelectedDataCall({ commit }) {
      await http
        .get('/asset_preferences.json')
        .then((res) => {
          const { cardData } = res.data;
          commit('setCardData', cardData);
        })
        .catch((error) => {
          commit('setErrorMessage', `Sorry, there was an error fetching asset preferences (${error.message}).`);
        });
    },
  },
};
