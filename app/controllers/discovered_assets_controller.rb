class DiscoveredAssetsController < AuthenticatedController
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include DiscoveredAssetLogs

  before_action :authorize_read, only: [:index, :show, :admin_assets, :discovered_assets]
  before_action :authorize_write, except: [:index, :show, :admin_assets, :discovered_assets]
  before_action :set_resource, only: [:show]
  after_action :create_asset_history, except: [:index, :show, :admin_assets, :discovered_assets]

  INTEGRATION_MAPPING = {
    'azure_ad_assets' => 'azure_ad_devices',
    'google_assets'   => 'google',
    'azure_assets'    => 'azure',
    'ms_intune_assets'=> 'ms_intune',
    'aws_assets'      => 'aws'
  }.freeze

  def index
    respond_to do |format|
      format.html
      format.json do
        json_response = {
          discovered_assets: discovered_assets,
          filtered_assets_count: @filtered_assets.count,
          page_count: @page_count,
          channel_key: "#{current_company.id}assets"
        }
        json_response[:filtered_assets] = @filtered_assets if params[:include_filtered_assets].present?
  
        render json: json_response
      end
    end
  end

  def show
    respond_to do |format|
      format.html {  }
      format.json { render json: @discovered_asset }
    end
  end

  def import
    if params[:asset].present?
      assets_service = BulkDiscoveredAssetsUpdate.new(params[:asset], current_company, scoped_company_user).import_assets
      render json: {}, status: :ok
    else
      render json: {}, status: :bad_request
    end
    Pusher.trigger("#{current_company.id}assets",'discovered-assets', {})
  end

  def bulk_archive
    if select_all
      BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "archive", params[:discovered_asset_ids].as_json, {}, params[:source].as_json, params[:search].as_json)
      render json: { message: 'Ignoring all the assets, this might take a while' }
    else
      ids = params[:discovered_asset_ids]
      if ids.present?
        assets = current_company.discovered_assets.where(id: ids)
        assets_service = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).archive_assets
        render json: {}, status: :ok
      else
        render json: { message: "Please include assets to archive." }, status: :bad_request
      end
    end
  end

  def bulk_delete
    if select_all
      BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "delete", params[:discovered_asset_ids].as_json, {}, params[:source].as_json, params[:search].as_json)
      render json: { message: 'Deleting all the assets, this might take a while' }
    else
      ids = params[:discovered_asset_ids]
      if ids.present?
        assets = current_company.discovered_assets.where(id: ids)
        assets_service = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).delete_assets
        render json: {}, status: :ok
      else
        render json: { message: "Please include assets to delete." }, status: :bad_request
      end
    end
  end

  def bulk_unarchive
    if select_all
      BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "unarchive", params[:discovered_asset_ids].as_json, {}, params[:source].as_json, params[:search].as_json)
      render json: { message: 'Unignoring all the assets, this might take a while' }
    else
      ids = params[:discovered_asset_ids]
      if ids.present?
        assets = current_company.discovered_assets.where(id: ids)
        assets_service = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).unarchive_assets
        render json: {}, status: :ok
      else
        render json: { message: "Please include assets to archive." }, status: :bad_request
      end
    end
  end

  def import_bulk
    assets = params[:discovered_assets]
    if assets.present?
      if select_all
        BulkUpdateDiscoveredAssetsWorker.perform_async(scoped_company.id, scoped_company_user.id, status, "import", assets.as_json, params[:update_all_fields].as_json, params[:source].as_json, params[:search].as_json)
        render json: { message: 'Importing all the assets, this might take a while' }
      else
        duplicate_asset_ids = BulkDiscoveredAssetsUpdate.new(assets, current_company, scoped_company_user).import_assets
        render json: { duplicate_asset_ids: duplicate_asset_ids }, status: :ok
      end
    else
      render json: {}, status: :bad_request
    end
  end

  def admin_assets
    assets = current_company.discovered_assets
    assets = assets.where(source: params[:source]) if params[:source].present?
    assets = assets.search_text(params[:search]) if params[:search].present?
    assets = assets.order(updated_at: :desc)

    @discovered_asset_count = assets.count
    @page_count = (assets.length / params[:page_size].to_f).ceil
    assets = assets.paginate(page: params[:page], per_page: params[:page_size]&.to_f)

    assets.map { |da| da.as_json.merge(
      asset_type: (da.asset_type.blank? ? 'Other' : da.asset_type))
    }

    render json: { discovered_assets: assets, discovered_asset_count: @discovered_asset_count, page_count: @page_count}
  end

  def discovered_asset_updates
    assets = current_company.discovered_assets.where(source: params[:source])
    asset_counts = company_assets_count(assets)

    respond_to do |format|
      format.html
      format.json do
        render json: {
          new_assets: asset_counts[:new_assets],
          check_assets: asset_counts[:check_assets],
          not_reporting: asset_counts[:not_reporting]
        }
      end
    end
  end

  def discovered_asset_updates_all
    alert_integrations = [
                          "Integrations::Ubiquity::Config",
                          "Integrations::AzureAdAssets::Config",
                          "Integrations::MsIntuneAssets::Config",
                          "Integrations::JamfPro::Config",
                          "Integrations::Meraki::Config",
                          "Integrations::Kaseya::Config",
                          "Integrations::Kandji::Config",
                          "Integrations::Mosyle::Config"
                        ]

    integrations = current_company
      .company_integrations
      .where(integrable_type: alert_integrations)
      .joins(:integration)
      .pluck('integrations.name', 'alert_info', 'sync_status')

    sources = integrations.map(&:first)
    assets = current_company.discovered_assets.where(source: sources)

    complete_info = integrations.map do |source, status_hash, sync_status|
      source_assets = assets.where(source: source)
      asset_counts = company_assets_count(source_assets)

      should_include = (
        (!status_hash["new_asset"] && asset_counts[:new_assets] > 0) ||
        (!status_hash["out_of_sync"] && asset_counts[:check_assets] > 0) ||
        (!status_hash["not_reporting"] && asset_counts[:not_reporting] > 0) ||
        (!status_hash["failed"] && sync_status === "failed")
      )    
      if should_include
        source
      end
    end.compact
    
    respond_to do |format|
      format.html
      format.json { render json: { complete_info: complete_info } }
    end
  end

  def discovered_assets
    assets = current_company.discovered_assets
    assets = assets.where(source: params[:source]) if params[:source].present?

    if params[:discovery].present? && params[:discovery] != 'all'
      assets =
        case params[:discovery]
        when 'new'
          assets.where(managed_asset_id: nil)
        when 'not_reporting_missing'
          assets.where.not(last_check_in_time: 7.days.ago.beginning_of_day..Time.current.end_of_day)
                .or(assets.where(last_check_in_time: nil))
                .where(status: 'imported')
        end
    end

    if params[:asset_type].present?
      if params[:asset_type] == "Other"
        assets = assets.where("asset_type = ? OR asset_type IS NULL OR asset_type = ?", "Other", "")
      else
        assets = assets.where(asset_type: params[:asset_type])
      end
    end

    if params[:location_id].present?
      assets = assets.includes(:integration_location)
      .where(:integrations_locations => {location_id: params[:location_id]})
    end
    assets = assets.search_text(params[:search]) if params[:search].present?

    if params[:active_order].present?
      active_order = JSON.parse(params[:active_order])
      sort_type = active_order["activeSort"].underscore
      if sort_type == "integrations_locations_id"
        order_condition = "integrations_locations.description #{active_order['activeSortDirection']}, id desc"
        assets = assets.where(status: status).left_joins(:integration_location).order(order_condition)
      else
        sort_type = "source" if sort_type == "asset_sources"
        sort_type = "display_name" if sort_type == "name"
        sort_type = "COALESCE(last_synced_at, updated_at)" if sort_type == 'last_synced_at'
        assets = assets.where(status: status).order(Arel.sql("#{sort_type} #{active_order["activeSortDirection"].underscore.to_sym}, id desc")) if assets.any?
      end
    else
      assets = assets.where(status: status).order(discovered_asset_type: :asc, id: :desc)
    end

    @filtered_assets = assets
    paginated_assets = assets.offset((page - 1) * page_size).limit(page_size)

    if params[:select_all_assets] && params[:offset]
      paginated_assets = assets.offset(params[:offset]).limit(params[:per_page])
    else
      @page_count = (assets.size / params[:page_size].to_f).ceil
    end
    
    paginated_assets = paginated_assets.includes(
      :asset_softwares,
      :asset_sources,
      :discovered_assets_hardware_detail,
      :cloud_asset_attributes,
      :integration_location,
      managed_asset: {  
        assignment_information: [:user, :manager]
      }
    )

    paginated_assets.map do |da|
      used_by_contributor = get_contributor(da.used_by)
      user_info, manager_info = get_user_manager(da)
      location_info = get_location_name(da)

      da.as_json.merge(
        location_description: da.integration_location&.description,
        location_id: da.integration_location&.location_id,
        asset_type: (da.asset_type.blank? ? 'Other' : da.asset_type),
        used_by_contributor_id: used_by_contributor&.id,
        used_by_contributor: used_by_contributor,
        managed_by_contributor_id: get_contributor(da.managed_by),
        department: nil,
        impact: nil,
        user_info: user_info,
        manager_info: manager_info,
        location_name: location_info
        )
    end
  end

  def select_all
    params[:select_all]
  end

  def status
    params[:discovery].present? ? ['ready_for_import', 'imported'] : params[:status]
  end

  def page
    (params[:page] || 1).to_i
  end

  def page_size
    (params[:page_size] || 25).to_i
  end

  def asset_count_by_type  
    render json: { asset_counts: asset_counts }
  end

  private
  def set_resource
    @discovered_asset = current_company.discovered_assets.find(params[:id])
  rescue ActiveRecord::RecordNotFound => e
    respond_to do |format|
      format.json { render json: { message: "Asset was not found." }, status: :not_found }
      format.html { render 'shared/not_found' }
    end
  end

  def get_contributor(owner_detail)
    if owner_detail.present?
      return Contributor.where(company_id: current_company.id).find_users_by_email(owner_detail).first
    end
  end

  def create_asset_history
    create_user_event_asset_log(params)
  end

  #add the name of the integrations which have the client's info to show
  def client_disc_asset_type_connectors?
    ["meraki", "ubiquiti"].include?(params[:source])
  end
  
  def asset_counts
    group_fields = [:asset_type]
    group_fields << :discovered_asset_type if client_disc_asset_type_connectors?
    group_fields.reverse!
    grouped_counts = current_company.discovered_assets
                                     .where(source: normalize_source(params[:source]))
                                     .group(*group_fields)
                                     .count
    return grouped_counts unless client_disc_asset_type_connectors?
  
    asset_counts = {}
    grouped_counts.each do |(discovered_asset_type, asset_type), count|
      key = %w[device sm_device].include?(discovered_asset_type) ? "device" : discovered_asset_type
      asset_counts[key] ||= {}
      asset_counts[key][asset_type || "Other"] ||= 0
      asset_counts[key][asset_type || "Other"] += count
    end
    asset_counts
  end

  def normalize_source(source)
    INTEGRATION_MAPPING.fetch(source, source)
  end

  def contributor_info(contributor)
    return nil unless contributor
  
    {
      full_name: contributor.name,
      email: contributor.email,
      avatar: contributor.avatar,
    }
  end
  
  def get_user_manager(das)
    if das.managed_asset&.assignment_information
      ai = das.managed_asset.assignment_information
      [contributor_info(ai.user), contributor_info(ai.manager)]
    else
      [nil, nil]
    end
  end

  def get_location_name(das)
    if das.managed_asset&.location
      das.managed_asset.location.name
    else 
      nil
    end
  end

  def company_assets_count(assets)  
    new_assets = assets.where(status: "ready_for_import", managed_asset_id: nil).count
    check_assets = assets.where(status: "imported", last_check_in_time: nil).count
    not_reporting = assets.where(status: "imported").where.not(last_check_in_time: 7.days.ago.beginning_of_day..Time.current.end_of_day).count
    {
      new_assets: new_assets,
      check_assets: check_assets,
      not_reporting: not_reporting
    }
  end
end
