module MosyleCommonMethods
  extend ActiveSupport::Concern
  include ReadReplicaDb

  included do
    def company
      set_read_replica_db do
        config.company
      end
    end

    def company_integration
      set_read_replica_db do
        config.company_integration
      end
    end

    def refresh_access_token
      client.refresh_access_token
    end

    def create_asset_source(device)
      asset_source_data = {
        display_name: device['device_name'],
        machine_serial_no: device['serial_number'],
        manufacturer: "Apple",
        os_name: device['os'],
        source: 'mosyle',
      }
      add_source(asset_source_data)
    end

    def add_source(asset_source_data)
      das = set_read_replica_db do
        AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :mosyle)
      end
      das.asset_data = asset_source_data
      das.company_integration_id = company_integration.id
      das.managed_asset_id = @discovered_asset.managed_asset_id
      das.save
    end

    def event_params(error, detail, api_type)
      {
        company_id: company.id,
        status: :error,
        error_detail: error.backtrace.join('\n'),
        class_name: self.class.name,
        integration_id: intg_id,
        api_type: api_type,
        error_message: error.message,
        detail: detail,
        response: '422',
        created_at: DateTime.now
      }
    end

    def intg_id
      @intg_id ||= set_read_replica_db do
        Integration.find_by_name('mosyle').id
      end
    end

    def assign_discovered_asset_attributes(device)
      @discovered_asset.assign_attributes(
        display_name: device['device_name'],
        mac_addresses: @mac_addresses,
        secondary_mac_addresses: @secondary_mac_addresses,
        ip_address: device['last_ip_beat'],
        manufacturer: "Apple",
        machine_serial_no: device['serial_number'],
        os_name: device['os'],
        os_version: device['osversion'],
        source: 'mosyle',
        system_uuid: device['deviceudid'],
        last_synced_at: DateTime.now,
        model: device['device_model_name'],
        lower_precedence: @is_lower_precedence,
        used_by: device['useremail'],
        asset_tag: device['asset_tag'],
        asset_type: get_asset_type(device['device_type']),
        integrations_locations_id: integration_location_id(device['location']),
        last_check_in_time: @last_check_in_time
      )
    end

    def integration_location_id(location_name)
      if location_name.present?
        company_integration_location = set_read_replica_db do
          @company.integrations_locations.find_or_initialize_by(address: location_name, source: 'mosyle')
        end
        company_integration_location.save if company_integration_location.new_record?
        company_integration_location&.id
      end
    end

    def get_asset_type(platform)
      if platform&.downcase.include?("computer")
        'Laptop'
      elsif platform&.downcase.include?("smartphone")
        'iPhone'
      elsif platform&.downcase.include?("tablet")
        'iPad'
      else
        'Apple Device'
      end
    end

    def assign_discovered_assets_hardware_details(device)
      @discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if @discovered_asset.discovered_assets_hardware_detail.blank?
      @discovered_asset.discovered_assets_hardware_detail.assign_attributes(
        memory: device['installed_memory'],
        processor: device['cpu_model'],
        hard_drive: device['total_disk']&.to_i,
        disk_free_space: device['available_disk']&.to_i,
        hostname: device['HostName'],
        imei: device['os'] == 'mac' ? device['imei'] : device['imeiOne'],
        hardware_version: device['device_model']
      )
    end

    def create_asset_software
      discovered_asset_softwares = set_read_replica_db do
        @discovered_asset.asset_softwares
      end
      return if is_lower_precedence && discovered_asset_softwares.present?

      if @discovered_asset.os_name.present?
        discovered_asset_softwares.find_or_initialize_by({
          software_type: 'Operating System',
          name: @discovered_asset.os_name
        })
      end
    end

    def precedence_data
      discovered_managed_asset_sources = set_read_replica_db do
        @discovered_asset.managed_asset&.sources
      end

      {
        asset_sources: discovered_managed_asset_sources,
        current_source: 'mosyle',
        discovered_asset: @discovered_asset,
        incoming_discovered_asset: DiscoveredAsset.mosyle.new
      }
    end

    def is_lower_precedence
      !is_higher_precedence?(**precedence_data)
    end

    def log_exception(exception, discovered_asset)
      Rails.logger.error(exception)
      Bugsnag.notify(exception) if (Rails.env.production? || Rails.env.staging?) && !exception.message.include?('Managed asset has already been taken')
      LogCreationWorker.perform_async('Logs::ApiEvent', event_params(exception, discovered_asset.to_json, 'save_devices').to_json)
    end
  end
end
