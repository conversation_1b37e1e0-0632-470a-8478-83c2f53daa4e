class Integrations::Mosyle::Config < ApplicationRecord
  include CompanyCache
  include DiscoveryToolsLogs

  self.table_name = "mosyle_configs"
  attr_accessor :skip_callbacks

  belongs_to :company
  belongs_to :company_user
  has_one :company_integration, as: :integrable, dependent: :destroy

  validates :username, :password, :access_token, :company_id, presence: true
  validates_uniqueness_of :company_id, message: "<PERSON><PERSON><PERSON> is already integrated"

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: Proc.new { |app| app.skip_callbacks }
  after_update :create_credentials_update_log
  before_destroy :destroy_integration_data

  enum import_type: [:managed_asset, :discovered_asset]

  def create_comp_intg_and_execute_job
    mosyle_integration = Integration.find_by(name: 'mosyle')
    comp_intg = CompanyIntegration.find_or_initialize_by(company_id: company_id,
                                                         integrable_type: 'Integrations::Mosyle::Config',
                                                         integrable_id: id,
                                                         integration_id: mosyle_integration.id)

    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!
    if self.refresh_token.present?
      Integrations::Mosyle::SyncDataWorker.perform_async(id, true, false, company_user_id)
    end
  end

  def destroy_integration_data
    self.company.discovered_assets.mosyle.where.not(status: :imported).destroy_all
  end

  def create_credentials_update_log
    if self.saved_changes?
      change_log = []
      if self.saved_changes.key?(:username)
        change_log << element_data("Username", self.saved_changes[:username]&.first, self.saved_changes[:username]&.last)
      end
      if self.saved_changes.key?(:password)
        change_log << element_data("Password", mask_value(self.saved_changes[:password]&.first), mask_value(self.saved_changes[:password]&.last))
      end
      if self.saved_changes.key?(:access_token)
        change_log << element_data("Access Token", mask_value(self.saved_changes[:access_token]&.first), mask_value(self.saved_changes[:access_token]&.last))
      end
      create_asset_connector_log(:credentials_updated, :mosyle, change_log, :successful, company_user_id, company_id) unless change_log.empty?
    end
  end
end
