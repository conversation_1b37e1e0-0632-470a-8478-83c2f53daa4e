module ManagedAssets
  module AssetsQuery
    class TagsClause < BaseClause
      def call(base)
        sort_by = @params[:sort_by]
        sort_order = @params[:sort_order]
        is_people_view = @params[:is_people] != 'false' if @params[:is_people]

        if (is_grid_view || (is_list_view && @selected_columns&.include?("tags"))) || is_people_view
          base = base.select(ManagedAssets::AssetsQuery::QueryConstants::ASSET_TAGS)
        end

        if @params[:active_tags].present? || @params[:tag].present?
          base = base.joins(ManagedAssets::AssetsQuery::QueryConstants::ASSET_TAGS_JOIN)
        
          if @params[:active_tags].present?
            base = base.where("company_asset_tags.id IN (?)", @params[:active_tags])
          elsif @params[:tag].present?
            base = base.where("company_asset_tags.id = '#{@params[:tag]}'")
          end
        end

        if sort_order && sort_by == "asset_tag" && is_list_view
          base = base.order("managed_assets.asset_tag #{sort_order} NULLS LAST")
        end

        base
      end
    end
  end
end
