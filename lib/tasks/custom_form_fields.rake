namespace :custom_form_fields do
  countries = [ "Aruba", "Afghanistan", "Angola", "Anguilla", "Åland Islands", "Albania", "Andorra", "United Arab Emirates", "Argentina", "Armenia", "American Samoa", "Antarctica", "French Southern Territories", "Antigua and Barbuda", "Australia", "Austria", "Azerbaijan", "Burundi", "Belgium", "Benin", "Bonaire, Sint Eustatius and Saba", "Burkina Faso", "Bangladesh", "Bulgaria", "Bahrain", "Bahamas", "Bosnia and Herzegovina", "Saint Barthélemy", "Belarus", "Belize", "Bermuda", "Bolivia", "Brazil", "Barbados", "Brunei Darussalam", "Bhutan", "Bouvet Island", "Botswana", "Central African Republic", "Canada", "Cocos (Keeling) Islands", "Switzerland", "Chile", "China", "Côte d'Ivoire", "Cameroon", "Congo, The Democratic Republic of the", "Congo", "Cook Islands", "Colombia", "Comoros", "Cabo Verde", "Costa Rica", "Cuba", "Curaçao", "Christmas Island", "Cayman Islands", "Cyprus", "Czechia", "Germany", "Djibouti", "Dominica", "Denmark", "Dominican Republic", "Algeria", "Ecuador", "Egypt", "Eritrea", "Western Sahara", "Spain", "Estonia", "Ethiopia", "Finland", "Fiji", "Falkland Islands (Malvinas)", "France", "Faroe Islands", "Micronesia, Federated States of", "Gabon", "United Kingdom", "Georgia", "Guernsey", "Ghana", "Gibraltar", "Guinea", "Guadeloupe", "Gambia", "Guinea-Bissau", "Equatorial Guinea", "Greece", "Grenada", "Greenland", "Guatemala", "French Guiana", "Guam", "Guyana", "Hong Kong", "Heard Island and McDonald Islands", "Honduras", "Croatia", "Haiti", "Hungary", "Indonesia", "Isle of Man", "India", "British Indian Ocean Territory", "Ireland", "Iran, Islamic Republic of", "Iraq", "Iceland", "Israel", "Italy", "Jamaica", "Jersey", "Jordan", "Japan", "Kazakhstan", "Kenya", "Kyrgyzstan", "Cambodia", "Kiribati", "Saint Kitts and Nevis", "Korea, Republic of", "Kuwait", "Lao People's Democratic Republic", "Lebanon", "Liberia", "Libya", "Saint Lucia", "Liechtenstein", "Sri Lanka", "Lesotho", "Lithuania", "Luxembourg", "Latvia", "Macao", "Saint Martin (French part)", "Morocco", "Monaco", "Moldova", "Madagascar", "Maldives", "Mexico", "Marshall Islands", "North Macedonia", "Mali", "Malta", "Myanmar", "Montenegro", "Mongolia", "Northern Mariana Islands", "Mozambique", "Mauritania", "Montserrat", "Martinique", "Mauritius", "Malawi", "Malaysia", "Mayotte", "Namibia", "New Caledonia", "Niger", "Norfolk Island", "Nigeria", "Nicaragua", "Niue", "Netherlands", "Norway", "Nepal", "Nauru", "New Zealand", "Oman", "Pakistan", "Panama", "Pitcairn", "Peru", "Philippines", "Palau", "Papua New Guinea", "Poland", "Puerto Rico", "Korea, Democratic People's Republic of", "Portugal", "Paraguay", "Palestine, State of", "French Polynesia", "Qatar", "Réunion", "Romania", "Russian Federation", "Rwanda", "Saudi Arabia", "Sudan", "Senegal", "Singapore", "South Georgia and the South Sandwich Islands", "Saint Helena, Ascension and Tristan da Cunha", "Svalbard and Jan Mayen", "Solomon Islands", "Sierra Leone", "El Salvador", "San Marino", "Somalia", "Saint Pierre and Miquelon", "Serbia", "South Sudan", "Sao Tome and Principe", "Suriname", "Slovakia", "Slovenia", "Sweden", "Eswatini", "Sint Maarten (Dutch part)", "Seychelles", "Syrian Arab Republic", "Turks and Caicos Islands", "Chad", "Togo", "Thailand", "Tajikistan", "Tokelau", "Turkmenistan", "Timor-Leste", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Tuvalu", "Taiwan", "Tanzania", "Uganda", "Ukraine", "United States Minor Outlying Islands", "Uruguay", "United States", "Uzbekistan", "Holy See (Vatican City State)", "Saint Vincent and the Grenadines", "Venezuela", "Virgin Islands, British", "Virgin Islands, U.S.", "Vietnam", "Vanuatu", "Wallis and Futuna", "Samoa", "Yemen", "South Africa", "Zambia", "Zimbabwe" ]
  states = [ "AA","AE","AK","AL","AP","AR","AS","AZ","CA","CO","CT","DC","DE","FL","GA","GU","HI","IA", "ID","IL","IN","KS","KY","LA","MA","MD","ME","MI","MN","MO","MP","MS","MT","NC","ND","NE", "NH","NJ","NM","NV","NY","OH","OK","OR","PA","PR","RI","SC","SD","TN","TX","UM","UT","VA", "VI","VT","WA","WI","WV","WY" ]

  desc "Add default values for priority and status"
  task add_default_values_to_priority_and_status: :environment do
    CustomFormField.where(field_attribute_type: "priority", default_value: nil).update_all(default_value: "low")
    CustomFormField.where(field_attribute_type: "status", default_value: nil).update_all(default_value: "Open")
  end

  desc "Rename duplicate custom_form_field names within each custom form"
  task rename_duplicate_fields: :environment do
    CustomForm.includes(:custom_form_fields).find_each do |custom_form|
      puts "Processing custom form #{custom_form.id}"
      fields = custom_form.custom_form_fields
      duplicates = fields.group_by(&:name).select { |_, records| records.size > 1 }
      next if duplicates.empty?

      existing_names = fields.pluck(:name).to_set
      duplicates.values.flatten.each_with_index do |record, index|
        next if index == 0

        base_name = record.name.sub(/(_\d+)$/, '')
        max_suffix = existing_names.map { |n| n.split('_').last.to_i }.max || 0
        new_name = "#{base_name}_#{max_suffix + 1}"
        new_name = "#{base_name}_#{existing_names.size + 1}" while existing_names.include?(new_name)
        existing_names.add(new_name)
        puts "Renaming #{record.name} to #{new_name}"
        record.update_columns(name: new_name)
      end
    end
  end

  desc "Set name field value"
  task set_name: :environment do
    CustomFormField.find_each do |field|
      name = "#{field.label}".parameterize.underscore
      field.update_columns(name: name)
    end
  end

  desc "Make created by field compulsory"
  task created_by_compulsory: :environment do
    CustomFormField.where(name: "created_by").update_all(required: true)
  end

  desc "Move attachment field to main view"
  task update_attachment_field_position: :environment do
    field_position_ids = FieldPosition.includes(:custom_form_field).where(custom_form_fields: { field_attribute_type: "attachment" }).pluck(:id)
    FieldPosition.where(id: field_position_ids).update_all(position: :main)
  end

  desc "Change people list options value from nil to '[]'"
  task set_options_null: :environment do
    CustomFormField.where(field_attribute_type: "people_list", options: nil).update_all(options: "[]")
  end

  desc "Remove invalid characters from name"
  task remove_invalid_name_characters: :environment do
    CustomFormField.find_each do |field|
      if field.name =~ /[^\w]/
        updated_name = field.name.gsub(/[^\w]/, '')
        field.update_columns(name: updated_name)
      end
    end
  end

  desc "Populate Country/State fields in custom forms"
  task populate_data_for_list_filed: :environment do
    CustomFormField.where(field_attribute_type: "list").each do |list|
      if (!list.options.blank?)
        options = JSON.parse(list.options)
        if (options[0].instance_of? Hash) && options.length >= 1
          if options[0]["name"] == "State/Province"
            options.shift
            list.update(options: options.concat(states).to_json, list_type: 'state')
          elsif options[0]["name"] == "Country"
            options.shift
            list.update(options: options.concat(countries).to_json, list_type: 'country')
          end
        end
      end
    end
  end

  desc "Add sort list values in all custom form fields"
  task add_sort_list_values_in_smart_list: :environment do
    smart_lists = %w{people_list asset_list contract_list vendor_list telecom_list location_list}
    smart_lists.each do |list|
      CustomFormField.where(field_attribute_type: list).each do |cff|
        if cff.sort_list.present?
          cff.update(sort_list: "[\"Ascending\", \"First Name, Last Name\"]")
        end
      end
    end
  end

  desc "Remove null options from custom form field list"
  task remove_null_options_from_list: :environment do
    CustomFormField.where(name: 'list').find_each do |l|
      l.update_columns(options: JSON.parse(l.options).compact.to_json) if JSON.parse(l.options).include?(nil)
    end
  end

  desc "Converts external user to people list"
  task convert_external_users: :environment do
    CustomFormField.where(field_attribute_type: :people_list, audience: nil).update_all(audience: 'staff')

    CustomFormField.where(field_attribute_type: :external_user).includes(custom_form: [ :company, :workspace ]).find_each do |field|
      company = field.custom_form.company
      workspace = field.custom_form.workspace
      field.custom_form_values.where.not(value_str: nil).find_each do |value|
        email = value.value_str
        if workspace
          guest = company.guests.find_or_create_by(email: email, workspace: workspace)
          value.value_str = nil
          value.value_int = guest.contributor_id
          value.save!
        end
      end
      field.field_attribute_type = :people_list
      field.audience = "guests"
      field.save!
    end
  end

  desc "Update created by field default value to current user which has no default value"
  task update_created_by_field_default_value: :environment do
    cff_ids = CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(name: 'created_by', field_attribute_type: :people_list, default_value: nil).pluck(:id)
    CustomFormField.where(id: cff_ids).update_all(default_value: "[\"current\"]")
  end

  desc "Update default value of audience for people list custom form fields"
  task update_default_audience_value: :environment do
    CustomFormField.where.not(name: 'created_by').where(field_attribute_type: 'people_list', audience: nil).update_all(audience: 'staff')
    CustomFormField.where(name: 'created_by', audience: nil).update_all(audience: 'guests')
  end

  desc "Update custom form field email type"
  task update_custom_form_field_email: :environment do
    CustomFormField.where(name: 'email').update_all(field_attribute_type: 'email')
  end

  desc "Set default options in the status and priority field where the options are nil"
  task set_options_in_status_and_priority: :environment do
    CustomFormField.where(field_attribute_type: ["status", "priority"], options: nil).where.not(msp_templates_custom_form_field_id: nil).find_each do |field|
      if field.msp_templates_custom_form_field.options.present?
        field.update_column(:options, field.msp_templates_custom_form_field.options)
      else
        options = field.field_attribute_type == "status" ? CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS : CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS
        field.update_column(:options, options)
      end
    end
  end

  desc 'Create expanded form field permission for every form field'
  task create_expanded_form_field_permission: :environment do
    CustomFormFieldPermission.find_each do |form_field_permission|
      form_field = form_field_permission.custom_form_field
      ExpandedCustomFormFieldPermissions::Populate.new(form_field_permission.contributor, form_field.id, form_field.custom_form.company_module).call
    end
  end

  desc 'Delete expanded form field permission for those fields that have only everyone group permission'
  task delete_everyone_group_expanded_permission: :environment do
    CustomFormField.joins(:custom_form).where(custom_form: {company_module: 'helpdesk'}).find_each do |field|
      p field.id
      field_permissions = field.custom_form_field_permissions
      contributor = field_permissions.first.contributor
      if field_permissions.count == 1 && contributor.contributor_type == 'Group' && contributor.group.name == 'Everyone'
        contributor_ids = field.custom_form.company.company_users.pluck(:contributor_id)
        ExpandedFormFieldPermission.where(contributor_id: contributor_ids, custom_form_field_id: field.id).destroy_all
      end
    end
  end

  desc 'Delete expanded form field permission for company users'
  task delete_company_user_field_permissions: :environment do
    CustomFormField.joins(:custom_form).where(custom_form: { company_module: 'company_user' }).find_each do |field|
      p field.id
      ExpandedFormFieldPermission.where(custom_form_field_id: field.id).destroy_all
    end    
  end

  desc 'Delete expanded form field permission for locations'
  task delete_location_field_permissions: :environment do
    CustomFormField.joins(:custom_form).where(custom_form: { company_module: 'location' }).find_each do |field|
      p field.id
      ExpandedFormFieldPermission.where(custom_form_field_id: field.id).destroy_all
    end
  end

  desc 'set helpdesk subject field to required'
  task set_subject_field_required: :environment do
    CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(name: "subject", required: false).find_each do |subject_field|
      subject_field.update_columns(required: true)
    end
  end

  desc "Fix custom form values and fields with special characters"
  task remove_special_characters_from_priority: :environment do
    def remove_special_characters(str)
      str.gsub(/[^a-zA-Z0-9]/, ' ').gsub(/\s+/, ' ')
    end

    CustomFormField.where(name: 'priority').each do |cff|
      old_value = JSON.parse(cff.options)

      new_value = old_value.map do |hash|
        modified_hash = {}
        hash.each do |key, value|
          modified_key = remove_special_characters(key)
          modified_hash[modified_key] = value
        end
        modified_hash
      end

      unless old_value == new_value
        cff.options = new_value.to_json
        cff.default_value = remove_special_characters(cff.default_value) != cff.default_value ? remove_special_characters(cff.default_value).downcase : cff.default_value
        cff.save

        cff.custom_form_values.each do |cfv|
          cfv.value_str = remove_special_characters(cfv.value_str) != cfv.value_str ? remove_special_characters(cfv.value_str).downcase : next
          cfv.save
        end
      end
    end
  end

  desc 'Update architecture of Priority field options'
  task update_priority_field_architecure: :environment do
    priority_fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(field_attribute_type: "priority")
    update_priority_field_options(priority_fields)

    msp_priority_fields = Msp::Templates::CustomFormField.includes(:msp_templates_custom_form).where(msp_templates_custom_forms: { company_module: "helpdesk" }).where(field_attribute_type: "priority")
    update_priority_field_options(msp_priority_fields)
  end

  desc 'Update architecture of custom form field template priority options'
  task update_template_priority_field_architecure: :environment do
    custom_form_fields_templates = CustomFormFieldTemplate.where(field_attribute_type: "priority")
    update_priority_field_options(custom_form_fields_templates)
  end

  def update_priority_field_options(fields)
    updated_field_ids = []
    unsuccessful_field_ids = []
    fields.find_each do |cff|
      options = JSON.parse(cff.options)
      new_options = []
      options.each do |opt|
        new_options << { "name" => opt.keys[0], "color" => opt[opt.keys[0]] }
      end
      cff.update_column(:options, new_options.to_json)
      updated_field_ids << cff.id
    rescue => e
      Rails.logger.warn "#{e.message}"
      unsuccessful_field_ids << cff.id
      next
    end
  end

  desc 'Add colors for help tickets statuses'
  task generate_status_colors: :environment do
    custom_form_fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(field_attribute_type: "status")
    update_status_fields_options(custom_form_fields)

    custom_form_fields_templates = CustomFormFieldTemplate.where(field_attribute_type: "status")
    update_status_fields_options(custom_form_fields_templates)

    msp_templates_custom_form_fields = Msp::Templates::CustomFormField.includes(:msp_templates_custom_form).where(msp_templates_custom_forms: { company_module: "helpdesk" }).where(field_attribute_type: "status")
    update_status_fields_options(msp_templates_custom_form_fields)
  end

  def generate_random_color
    "#" + format("%06x", (rand * 0xffffff))
  end

  def default_status_options
    [
      {"name"=>"Open", "color"=>"#2ECC71"},
      {"name"=>"In Progress", "color"=>"#5DADE2"},
      {"name"=>"Closed", "color"=>"#626567"}
    ]
  end

  def update_status_fields_options fields
    updated_field_ids = []
    unsuccessful_field_ids = []
    fields.find_each do |cff|
      custom_form_field_options = cff.options
      my_options = JSON.parse(custom_form_field_options)

      updated_options = my_options.map do |option|
        default_option = default_status_options.find { |default_opt| default_opt["name"].downcase == option.downcase }
        default_option.present? ? default_option : { "name" => option, "color" => generate_random_color }
      end

      cff.update_column(:options, updated_options.to_json)
      updated_field_ids << cff.id
    rescue => e
      Rails.logger.warn "#{e.message}"
      unsuccessful_field_ids << cff.id
      next
    end
  end

  desc 'Set default value for empty audience in created_by and assigned_to fields'
  task set_default_audience: :environment do
    created_by_fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(name: "created_by").where(audience: nil)
    update_audience(created_by_fields, 'guests')

    assigned_to_fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(name: "assigned_to").where(audience: nil)
    update_audience(assigned_to_fields, 'staff')

    def update_audience(fields, value)
      fields.find_each do |cff|
        cff.update_column(:audience, value)
      end
    end
  end

  desc 'Update custom form priority and status field missing options which copied from msp'
  task populate_msp_copied_form_missing_field_options: :environment do
    CustomForm.where(company_module: "helpdesk").where.not(msp_templates_custom_form_id: nil).find_each do |custom_form|
      update_custom_form_field_options("priority", custom_form)
      update_custom_form_field_options("status", custom_form)
    end
  end

  def update_custom_form_field_options(field_type, custom_form) 
    cf_field_values = custom_form.help_tickets.pluck(field_type).uniq
    field = custom_form.custom_form_fields.find_by(field_attribute_type: field_type, name: field_type)
    if field&.present?
      options = JSON.parse(field.options)
      options_names = options.pluck("name")
      fields_names = cf_field_values - options_names
      puts "company_id #{custom_form.company_id}"
      fields_names.each do |name|
        options << { "name" => name, "color" => generate_random_color }
        field.update_columns(options: options.to_json)
      end
    end
  end
  
  desc 'Change audience value to "teammates" for fields with audience "staff"'
  task change_audience_to_teammates: :environment do
    CustomFormField.where(audience: 'staff', field_attribute_type: :people_list).update_all(audience: 'teammates')
  end

  desc "Create option in status and priority field for nil"
  task create_options_for_nil: :environment do
    empty_status_options = CustomFormField.where(field_attribute_type: "status", name: 'status', options: nil).pluck(:id)
    empty_priority_options = CustomFormField.where(field_attribute_type: "priority", name: 'priority', options: nil).pluck(:id)

    empty_options_field_ids = empty_status_options + empty_priority_options

    CustomFormField.where(id: empty_options_field_ids).find_each do |field|
      default_options = field.field_attribute_type == "status" ? CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS : CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS
      field.update_column(:options, default_options)
    end
  end

  desc "Add missing default options for status and priority"
  task add_missing_default_options: :environment do
    update_form_fields(CustomFormField.where(field_attribute_type: ["status", "priority"], name: ['status', 'priority']))
    update_form_fields(Msp::Templates::CustomFormField.where(field_attribute_type: ["status", "priority"], name: ['status', 'priority']))
  end

  desc "update private attribute for required fields "
  task update_required_fields_private_attribute: :environment do
    custom_form_fields = CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(required: true, private: true)
    custom_form_fields.update_all(private: false) if custom_form_fields.any?
  end

  desc "trim extra spaces from options in checkboxes"
  task trim_extra_spaces_from_options_in_checkboxes: :environment do
    CustomFormField.where(field_attribute_type: "checkbox").find_each do |field|
      begin
        options = JSON.parse(field.options)
        trimmed_options = options.map(&:strip)
        field.update_attribute(:options, trimmed_options.to_json)
      rescue => e
        puts "faulty_field: #{field.id} , exception: #{e.message}"
        next
      end
    end
  end

  desc "delete impacted_users field"
  task delete_impacted_users_field: :environment do
    custom_form_fields = CustomFormField.joins(:custom_form)
                                     .where(name: "impacted_users", field_attribute_type: 'people_list', custom_forms: { company_module: "helpdesk" })
    custom_form_fields.destroy_all
  end

  task :copy_fields_one_form_to_another, [:source_form_id, :target_form_id] => :environment do |t, args|
    source_form_id = args[:source_form_id]
    target_form_id = args[:target_form_id]

    source_form = CustomForm.find_by(id: source_form_id)
    target_form = CustomForm.find_by(id: target_form_id)
    company_id = target_form.company_id

    if source_form && target_form
      source_form.custom_form_fields.each do |source_field|
        next if source_field.name == "subject" || source_field.name == "created_by"
        target_field = target_form.custom_form_fields.find_by(label: source_field.label, field_attribute_type: source_field.field_attribute_type)
        if target_field
          TicketListColumn.where(company_id: company_id).each do |column|
            col = column.columns.find { |col| col["field_name"] == target_field.name }
            if col
              col["field_name"] = source_field.name
              column.save
            end
          end
          target_field.update(name: source_field.name)
        end
      end      
    else
      puts "Source form or target form not found!"
    end
  end

  task :update_fields_type, [:custom_form_id, :source_field_type, :target_field_type, :fields_to_update] => :environment do |t, args|
    custom_form_id = args[:custom_form_id]
    source_field_type = args[:source_field_type]
    target_field_type = args[:target_field_type]
    fields_to_update = args[:fields_to_update].split(':')
    custom_form = CustomForm.find_by(id: custom_form_id)

    if custom_form
      fields_to_update.each do |field_label|
        field = custom_form.custom_form_fields.find_by(label: field_label, field_attribute_type: source_field_type)
        if field
          field.update(field_attribute_type: target_field_type)
        end
      end
    else
      puts "Custom form not found!"
    end
  end  

  desc "Update default field 'name' of a custom form to custom field names"
  task update_field_names_with_custom_names: :environment do
    # Map the custom values with default value in 'name' column of the fields
    custom_form_id = 35834
    names_hash = {
      "text" => "name_of_employee",
      "text_5" => "manager_name",
      "text_3" => "job_title",
      "radio_button" => "department",
      "checkbox" => "applications"
    }
    custom_form_fields = CustomFormField.where(custom_form_id: custom_form_id, name: names_hash.keys)
    custom_form_fields.each do |field| 
      field.update_column(:name, names_hash[field.name])
    end
    custom_form = CustomForm.find(custom_form_id)
    ticket_list_columns = TicketListColumn.where(company_id: custom_form.company_id, workspace_id: custom_form.workspace_id)
    ticket_list_columns.each do |columns|
      columns['columns'].each do |col|
        if names_hash.keys.include?(col['field_name'])
          col['field_name'] = names_hash[col['field_name']]
        end 
      end
      columns.save!
    end
  end

  def update_form_fields(fields)
    fields.find_each do |field|
      default_options = JSON.parse(field.field_attribute_type == "status" ? CustomFormFieldTemplate::DEFAULT_STATUS_OPTIONS : CustomFormFieldTemplate::DEFAULT_PRIORITY_OPTIONS)
      existing_options = JSON.parse(field.options || '[]')

      missing_options = default_options.reject { |opt| existing_options.any? { |existing_opt| existing_opt['name'] == opt['name'] } }

      if missing_options.present?
        existing_options.concat(missing_options.uniq { |opt| opt['name'] })
        field.update_column(:options, existing_options.to_json)
      end
    end
  end

  desc "Add the missing option for the status field, which is present on ticket status."
  task missing_status_options: :environment do
    unsuccessful_ticket_ids = []
    form_data = []
    HelpTicket.find_each do |ht|
      begin
        puts(ht.id)
        status_field = form_data[ht.custom_form_id]
        if status_field.nil?
          form_data[ht.custom_form_id] = get_status_field(ht.custom_form)
        end
        options = JSON.parse(status_field.options)

        option_exists = options.any? { |option| option['name'] == ht.status }

        unless option_exists
          options << { "name" => ht.status, 'color' => generate_random_color }
          status_field.update_column(:options, options.to_json)
        end
      rescue => e
        puts('error')
        Rails.logger.warn "#{e.message}"
        unsuccessful_ticket_ids << { id: ht.id, message: e.message }
        next
      end
    end
  end

  desc 'Add missing status field in helpdesk custom forms.'
  task missing_status_field: :environment do
    CustomForm.where(company_module: 'helpdesk').find_each do |form|
      puts(form.id)
      position = form.custom_form_fields.pluck(:order_position).max
      status_field = get_status_field(form)
      if status_field.nil?
        cff = CustomFormField.new(
          custom_form_id: form.id,
          field_attribute_type: 'status',
          label: "Status",
          options: default_status_options.to_json,
          required: true,
          default_value: "Open",
          permit_view_default: true,
          permit_edit_default: true,
          name: "status",
          order_position: position + 1
        )
        cff.skip_event_logs = true
        cff.save

        cfp = FieldPosition.new(custom_form_field_id: cff.id, position: 'right')
        cfp.skip_event_logs = true
        cfp.save
      end
    end
  end

  desc 'Add missing default values in status field options.'
  task missing_default_status_options: :environment do
    status_fields = CustomFormField.joins(:custom_form).where(custom_form: { company_module: 'helpdesk' }, name: 'status')
    status_fields.find_each do |form_field|
      puts(form_field.id)
      options = JSON.parse(form_field.options)
    
      default_status_options.each do |default_option|
        unless options.any? { |option| option["name"] == default_option["name"] }
          options << default_option
        end
      end
    
      if options != JSON.parse(form_field.options)
        form_field.update_column(:options, options.to_json)
      end
    end
  end

  desc 'Update is_followers_field to true for followers in IT General Request custom form'
  task update_followers_field_in_it_general_request_form: :environment do
    CustomFormField.joins(:custom_form).where(name: 'followers', custom_form: { form_name: "IT General Request", company_module: "helpdesk" }).update_all(is_followers_field: true)
  end

  def get_status_field(form)
    form.custom_form_fields.find_by(name: 'status')
  end

  desc "Replace slashes in field names with underscores"
  task update_field_names: :environment do
    fields_with_slash = CustomFormField.where("name LIKE ?", "%/%")

    fields_with_slash.each do |field|
      new_name = field.name.tr('/', '_')
      puts "Updating Field Name: #{field.name} -> #{new_name}"
      field.update_column(:name, new_name)
    end    
  end

  desc "Rename all is_followers_field fields to 'followers', safely, for helpdesk module only"
  task update_name_for_followers_fields: :environment do
    fields_to_fix = CustomFormField
                      .joins(:custom_form)
                      .where(is_followers_field: true)
                      .where.not(name: "followers")
                      .where(custom_forms: { company_module: "helpdesk" })
    
    puts "Found #{fields_to_fix.count} custom form fields to review..."

    updated_count = 0
    skipped_count = 0

    fields_to_fix.find_each do |field|
      form = field.custom_form

      if form.custom_form_fields.where(name: "followers").present?
        puts "Skipped field ID #{field.id} (form ID #{form.id}) — 'followers' field already exists."
        skipped_count += 1
        next
      end

      field.update_columns(name: "followers")
      puts "Renamed field ID #{field.id} in form ID #{form.id}"
      updated_count += 1
    rescue => e
      puts "Error updating field ID #{field.id}: #{e.message}"
      skipped_count += 1
      next
    end

    puts "Done. Renamed #{updated_count} fields. Skipped #{skipped_count} fields."
  end

  desc "Update default priority color in CustomFormFields and MspTemplatesCustomFormField"
  task update_default_priority_colors: :environment do
    puts "Start updating option colors!"

    priority_fields= CustomFormField.includes(:custom_form).where(custom_forms: { company_module: "helpdesk" }).where(field_attribute_type: "priority")
    process_fields(priority_fields)

    msp_priority_fields = Msp::Templates::CustomFormField.includes(:msp_templates_custom_form).where(msp_templates_custom_forms: { company_module: "helpdesk" }).where(field_attribute_type: "priority")
    process_fields(msp_priority_fields)

    puts "Option colors update complete!"
  end

  def process_fields(fields)
    color_updates = {
      'low' => '#0d6efd',
      'medium' => '#fd7e14',
      'high' => '#e14144'
    }

    fields.find_each do |field|
      begin
        options = JSON.parse(field.options)
        options.each do |option|
          if color_updates.key?(option['name'])
            option['color'] = color_updates[option['name']]
          end
        end
        field.update_column(:options, options.to_json)
        puts "Updated field :#{field.id}"
      rescue => e
        puts "Skipping field #{field.id} due to: #{e.message}"
      end
    end
  end

  desc "Update default priority color in CustomFormFieldsTemplate"
  task update_default_priority_colors_in_custom_form_field_template: :environment do
    puts "Start updating option colors!"
    fields = CustomFormFieldTemplate.where(field_attribute_type: "priority")
    fields.find_each do |field|
      begin
        field.update_column(:options, "[{\"name\":\"low\",\"color\":\"#0d6efd\"},{\"name\":\"medium\",\"color\":\"#fd7e14\"},{\"name\":\"high\",\"color\":\"#e14144\"}]")
        puts "Updated field :#{field.id}"
      rescue => e
        puts "Skipping field #{field.id} due to: #{e.message}"
      end
    end
    puts "Option colors update complete!"
  end

  desc "Update 'people_list' audience attribute for helpdesk and company_user modules."
  task update_people_list_audience: :environment do
    puts "Start updating audience for helpdesk and company_user modules"
    helpdesk_fields = CustomFormField.joins(:custom_form)
      .where(custom_forms: { company_module: "helpdesk" })
      .where(field_attribute_type: "people_list", audience: nil)
    followers_fields = helpdesk_fields.where(name: "followers")
    other_helpdesk_fields = helpdesk_fields.where.not(name: "followers")
    puts "Found \\#{followers_fields.count} 'followers' fields with nil audience in helpdesk forms"
    followers_fields.update_all(audience: "guests")
    puts "Found \\#{other_helpdesk_fields.count} other people_list fields with nil audience in helpdesk forms"
    other_helpdesk_fields.update_all(audience: "teammates")
    company_user_fields = CustomFormField.joins(:custom_form)
      .where(custom_forms: { company_module: "company_user" })
      .where(field_attribute_type: "people_list", audience: nil)
    puts "Found \\#{company_user_fields.count} people_list fields with nil audience in company_user forms"
    company_user_fields.update_all(audience: "teammates")
    puts "Audience update complete!"
  end

  desc "Update followers field audience from 'teammates' to 'guests'"
  task update_followers_audience: :environment do
    count = CustomFormField.where(is_followers_field: true, audience: "teammates").update_all(audience: "guests")
    puts "Updated #{count} CustomFormField record(s)."
  end
end
